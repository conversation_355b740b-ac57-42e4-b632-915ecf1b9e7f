"""
Debug script to identify import hanging issues.
"""

import os
import sys
import time

def test_import_step_by_step():
    """Test imports step by step to find where it hangs."""
    print("🔍 Testing imports step by step...")
    
    try:
        print("1. Testing basic Python imports...")
        import torch
        print("✅ torch imported")
        
        import json
        print("✅ json imported")
        
        import logging
        print("✅ logging imported")
        
        print("\n2. Testing AI brain model imports...")
        from ai_brain_model.utils.logging_config import setup_logging
        print("✅ logging_config imported")
        
        from ai_brain_model.utils.error_tracker import setup_enhanced_error_handling
        print("✅ error_tracker imported")
        
        print("\n3. Testing Nova model integration import...")
        print("   Importing nova_model_integration...")
        start_time = time.time()
        
        from nova_model_integration import NovaModelWrapper
        
        import_time = time.time() - start_time
        print(f"✅ nova_model_integration imported in {import_time:.2f}s")
        
        print("\n4. Testing Nova integration import...")
        print("   Importing nova_integration...")
        start_time = time.time()
        
        from nova_integration import NovaAI
        
        import_time = time.time() - start_time
        print(f"✅ nova_integration imported in {import_time:.2f}s")
        
        print("\n🎉 All imports successful!")
        return True
        
    except Exception as e:
        print(f"❌ Import failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_model_creation():
    """Test creating the model wrapper."""
    print("\n🔍 Testing model wrapper creation...")
    
    try:
        from nova_model_integration import NovaModelWrapper
        
        print("Creating NovaModelWrapper...")
        start_time = time.time()
        
        model_wrapper = NovaModelWrapper(
            model_path="output_expanded/nova_model_expanded_epoch_17.pt",
            tokenizer_path="output_bpe/nova_bpe_tokenizer.json",
            use_memory_optimization=True
        )
        
        creation_time = time.time() - start_time
        print(f"✅ Model wrapper created in {creation_time:.2f}s")
        
        return True
        
    except Exception as e:
        print(f"❌ Model creation failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_nova_ai_creation():
    """Test creating Nova AI integration."""
    print("\n🔍 Testing Nova AI integration creation...")
    
    try:
        from nova_model_integration import NovaModelWrapper
        from nova_integration import NovaAI
        
        print("Creating model wrapper...")
        model_wrapper = NovaModelWrapper(
            model_path="output_expanded/nova_model_expanded_epoch_17.pt",
            tokenizer_path="output_bpe/nova_bpe_tokenizer.json",
            use_memory_optimization=True
        )
        
        print("Creating Nova AI integration...")
        start_time = time.time()
        
        nova = NovaAI(
            model_function=model_wrapper.generate_text,
            knowledge_base_path="nova_knowledge_base.db",
            enable_tools=True
        )
        
        creation_time = time.time() - start_time
        print(f"✅ Nova AI created in {creation_time:.2f}s")
        
        return True
        
    except Exception as e:
        print(f"❌ Nova AI creation failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run debug tests."""
    print("🚀 Nova Import Debug Tool")
    print("=" * 40)
    
    # Test each step
    tests = [
        ("Step-by-step imports", test_import_step_by_step),
        ("Model wrapper creation", test_model_creation),
        ("Nova AI creation", test_nova_ai_creation)
    ]
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        
        try:
            success = test_func()
            if not success:
                print(f"❌ {test_name} failed - stopping here")
                break
        except Exception as e:
            print(f"❌ {test_name} crashed: {e}")
            import traceback
            traceback.print_exc()
            break
    
    print(f"\n{'='*40}")
    print("🔍 Debug complete. Check output above for issues.")

if __name__ == "__main__":
    main()
    input("Press Enter to continue...")
