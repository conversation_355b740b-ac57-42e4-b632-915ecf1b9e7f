@echo off
setlocal enabledelayedexpansion

echo ===================================
echo Nova AI Launcher
echo ===================================

:: Set the path to the virtual environment
set VENV_PATH=venv

:: Check if the virtual environment exists
if not exist %VENV_PATH%\Scripts\activate.bat (
    echo Virtual environment not found at %VENV_PATH%
    echo Creating a new virtual environment...
    python -m venv %VENV_PATH%

    if not exist %VENV_PATH%\Scripts\activate.bat (
        echo Failed to create virtual environment.
        echo Please make sure Python is installed and try again.
        pause
        exit /b 1
    )
)

:: Check if the virtual environment is already activated
if "!VIRTUAL_ENV!" == "" (
    echo Activating virtual environment...
    call %VENV_PATH%\Scripts\activate.bat
) else (
    echo Virtual environment is already activated.
)

:: Check if required packages are installed
echo Checking required packages...

:: List of required packages
set PACKAGES=torch numpy spacy tqdm nltk scikit-learn

:: Install missing packages
for %%p in (%PACKAGES%) do (
    python -c "import %%p" 2>nul
    if !errorlevel! neq 0 (
        echo Installing %%p...
        pip install %%p
    )
)

:: Check if model files exist
echo Checking model files...

set MODEL_PATH=output_expanded\nova_model_expanded_epoch_17.pt
set TOKENIZER_PATH=output_bpe\nova_bpe_tokenizer.json

if not exist %MODEL_PATH% (
    echo ERROR: Model file not found at %MODEL_PATH%
    echo Please make sure the model file is in the correct location.
    pause
    exit /b 1
)

if not exist %TOKENIZER_PATH% (
    echo ERROR: Tokenizer file not found at %TOKENIZER_PATH%
    echo Please make sure the tokenizer file is in the correct location.
    pause
    exit /b 1
)

:: Check if knowledge base exists
set KB_PATH=nova_knowledge_base.db
if not exist %KB_PATH% (
    echo Knowledge base not found. Creating it...
    python populate_knowledge_base.py

    if not exist %KB_PATH% (
        echo WARNING: Failed to create knowledge base.
        echo Nova will continue but may have limited conversational abilities.
    )
)

:: Create logs directory if it doesn't exist
if not exist logs (
    mkdir logs
    echo Created logs directory.
)

:: Check if fixes have been applied
echo Checking Nova AI fixes...
if not exist test_nova_fixes.py (
    echo Applying Nova AI fixes...
    python fix_nova_issues.py
    if !errorlevel! neq 0 (
        echo ERROR: Failed to apply fixes.
        pause
        exit /b 1
    )
)

:: Run the Nova AI
echo ===================================
echo Starting Nova AI...
echo ===================================
echo Note: If you encounter issues, the fixes have been applied to nova_model_integration.py
echo The main issue (parameter mismatch) should now be resolved.
echo.
python run_nova.py

:: Deactivate the virtual environment
echo ===================================
echo Shutting down Nova AI...
echo ===================================
call %VENV_PATH%\Scripts\deactivate.bat

echo Done.
pause
