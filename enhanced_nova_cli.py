"""
Enhanced Nova CLI with Comprehensive Error Tracking and Debugging.

This script provides an improved command-line interface for Nova AI with:
- Detailed error logging and tracking
- Enhanced debugging capabilities
- Better error reporting and recovery
- Comprehensive logging of all operations
"""

import os
import sys
import time
import argparse
import logging
from datetime import datetime

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import Nova components
from ai_brain_model.utils.logging_config import setup_logging, get_logger
from ai_brain_model.utils.error_tracker import (
    setup_enhanced_error_handling, 
    track_errors, 
    log_user_interaction_error,
    log_model_error,
    log_system_error,
    print_error_summary,
    get_recent_errors
)

# Set up enhanced logging and error handling
setup_logging(
    log_level="debug",  # Enable debug logging
    log_to_console=True,
    log_to_file=True,
    log_file_name="nova_enhanced.log",
    enable_debug_mode=True
)

setup_enhanced_error_handling()
logger = get_logger(__name__)

class EnhancedNovaInterface:
    """
    Enhanced Nova interface with comprehensive error tracking and debugging.
    """
    
    def __init__(self):
        """Initialize the enhanced Nova interface."""
        logger.info("🚀 Initializing Enhanced Nova Interface...")
        self.nova = None
        self.model_wrapper = None
        self.session_start_time = time.time()
        self.turn_count = 0
        
    @track_errors("nova_initialization")
    def initialize_nova(self, model_path: str, tokenizer_path: str, kb_path: str):
        """
        Initialize Nova with enhanced error tracking.
        
        Args:
            model_path: Path to the model file
            tokenizer_path: Path to the tokenizer file
            kb_path: Path to the knowledge base
        """
        logger.info("🔧 Starting Nova initialization...")
        
        # Check if files exist
        self._verify_files(model_path, tokenizer_path, kb_path)
        
        try:
            # Import Nova components
            logger.debug("📦 Importing Nova components...")
            from nova_model_integration import NovaModelWrapper
            from nova_integration import NovaAI
            
            # Create model wrapper
            logger.info(f"🤖 Creating model wrapper with {model_path}")
            self.model_wrapper = NovaModelWrapper(
                model_path=model_path,
                tokenizer_path=tokenizer_path,
                use_memory_optimization=True
            )
            logger.info("✅ Model wrapper created successfully")
            
            # Create Nova AI integration
            logger.info(f"🧠 Creating Nova AI integration with KB: {kb_path}")
            self.nova = NovaAI(
                model_function=self.model_wrapper.generate_text,
                knowledge_base_path=kb_path,
                enable_tools=True
            )
            logger.info("✅ Nova AI integration created successfully")
            
            # Start a session
            session_id = self.nova.start_session()
            logger.info(f"🎯 Started Nova session: {session_id}")
            
        except Exception as e:
            error_id = log_system_error(e, "nova_initialization")
            logger.error(f"❌ Failed to initialize Nova - Error ID: {error_id}")
            raise
    
    def _verify_files(self, model_path: str, tokenizer_path: str, kb_path: str):
        """Verify that required files exist."""
        logger.debug("🔍 Verifying required files...")
        
        files_to_check = [
            ("Model", model_path),
            ("Tokenizer", tokenizer_path),
            ("Knowledge Base", kb_path)
        ]
        
        missing_files = []
        for file_type, file_path in files_to_check:
            if not os.path.exists(file_path):
                missing_files.append(f"{file_type}: {file_path}")
                logger.error(f"❌ Missing {file_type}: {file_path}")
            else:
                logger.debug(f"✅ Found {file_type}: {file_path}")
        
        if missing_files:
            error_msg = f"Missing required files: {', '.join(missing_files)}"
            raise FileNotFoundError(error_msg)
    
    @track_errors("message_processing")
    def process_message(self, user_input: str) -> str:
        """
        Process a user message with enhanced error tracking.
        
        Args:
            user_input: The user's input message
            
        Returns:
            Nova's response
        """
        self.turn_count += 1
        logger.info(f"🔄 Turn {self.turn_count}: Processing message")
        logger.debug(f"📝 User input: '{user_input}'")
        
        if not self.nova:
            raise RuntimeError("Nova not initialized. Call initialize_nova() first.")
        
        try:
            start_time = time.time()
            
            # Process the message
            result = self.nova.process_message(user_input)
            
            processing_time = time.time() - start_time
            response = result.get('response', 'No response generated')
            
            logger.info(f"✅ Message processed in {processing_time:.2f}s")
            logger.debug(f"🎯 Response: '{response[:100]}...'")
            
            return response
            
        except Exception as e:
            error_id = log_user_interaction_error(e, user_input, "message_processing")
            logger.error(f"❌ Failed to process message - Error ID: {error_id}")
            
            # Return a helpful error message to the user
            return f"I'm sorry, I encountered an error while processing your message. Error ID: {error_id}. Please try again or rephrase your question."
    
    def interactive_session(self):
        """Start an interactive session with enhanced error handling."""
        logger.info("🎮 Starting interactive session...")
        
        print("\n" + "="*60)
        print("🌟 NOVA AI - Enhanced Interactive Session")
        print("="*60)
        print("Commands:")
        print("  'exit' or 'quit' - End the session")
        print("  'errors' - Show recent errors")
        print("  'debug' - Toggle debug mode")
        print("  'help' - Show this help message")
        print("="*60 + "\n")
        
        debug_mode = False
        
        while True:
            try:
                # Get user input
                user_input = input("\n💬 You: ").strip()
                
                # Handle special commands
                if user_input.lower() in ['exit', 'quit', 'bye', 'goodbye']:
                    logger.info("👋 User requested exit")
                    response = self.process_message("Goodbye!")
                    print(f"🌟 Nova: {response}")
                    break
                
                elif user_input.lower() == 'errors':
                    print_error_summary()
                    recent_errors = get_recent_errors(3)
                    if recent_errors:
                        print("\n📋 Recent Errors:")
                        for i, error in enumerate(recent_errors[-3:], 1):
                            print(f"  {i}. {error['error_type']}: {error['error_message']}")
                            print(f"     Context: {error['context']}")
                            print(f"     Time: {error['timestamp']}")
                    continue
                
                elif user_input.lower() == 'debug':
                    debug_mode = not debug_mode
                    print(f"🔧 Debug mode: {'ON' if debug_mode else 'OFF'}")
                    continue
                
                elif user_input.lower() == 'help':
                    print("\n📖 Available Commands:")
                    print("  'exit' or 'quit' - End the session")
                    print("  'errors' - Show recent errors and summary")
                    print("  'debug' - Toggle debug output")
                    print("  'help' - Show this help message")
                    print("\n💡 Just type your message to chat with Nova!")
                    continue
                
                elif not user_input:
                    print("💭 Please enter a message or command.")
                    continue
                
                # Process the message
                start_time = time.time()
                print("🤔 Thinking...", end='', flush=True)
                
                response = self.process_message(user_input)
                
                processing_time = time.time() - start_time
                
                # Clear thinking message
                print("\r" + " " * 15 + "\r", end='', flush=True)
                
                # Display response
                print(f"🌟 Nova: {response}")
                
                if debug_mode:
                    print(f"⏱️  Processing time: {processing_time:.2f}s")
                    print(f"🔢 Turn: {self.turn_count}")
                
            except KeyboardInterrupt:
                logger.info("🛑 Session interrupted by user")
                print("\n\n👋 Session interrupted. Goodbye!")
                break
            
            except Exception as e:
                error_id = log_system_error(e, "interactive_session")
                logger.error(f"❌ Unexpected error in interactive session - Error ID: {error_id}")
                print(f"\n🚨 An unexpected error occurred (Error ID: {error_id})")
                print("The session will continue. Type 'errors' to see details.")
        
        # Session summary
        session_time = time.time() - self.session_start_time
        logger.info(f"📊 Session ended after {session_time:.1f}s, {self.turn_count} turns")
        print(f"\n📊 Session Summary:")
        print(f"   Duration: {session_time:.1f} seconds")
        print(f"   Turns: {self.turn_count}")
        print_error_summary()

def main():
    """Main function for the enhanced CLI."""
    parser = argparse.ArgumentParser(description="Enhanced Nova AI CLI with Error Tracking")
    parser.add_argument("--model", type=str, 
                       default="output_expanded/nova_model_expanded_epoch_17.pt",
                       help="Path to model checkpoint")
    parser.add_argument("--tokenizer", type=str, 
                       default="output_bpe/nova_bpe_tokenizer.json",
                       help="Path to tokenizer file")
    parser.add_argument("--kb", type=str, 
                       default="nova_knowledge_base.db",
                       help="Path to knowledge base")
    
    args = parser.parse_args()
    
    logger.info("🚀 Starting Enhanced Nova CLI...")
    logger.info(f"📁 Model: {args.model}")
    logger.info(f"📁 Tokenizer: {args.tokenizer}")
    logger.info(f"📁 Knowledge Base: {args.kb}")
    
    try:
        # Create and initialize the interface
        interface = EnhancedNovaInterface()
        interface.initialize_nova(args.model, args.tokenizer, args.kb)
        
        # Start interactive session
        interface.interactive_session()
        
    except Exception as e:
        error_id = log_system_error(e, "main_initialization")
        logger.error(f"❌ Failed to start Nova CLI - Error ID: {error_id}")
        print(f"\n🚨 Failed to start Nova: {e}")
        print(f"Error ID: {error_id}")
        print("Check the logs for detailed error information.")
        return 1
    
    return 0

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
