"""
Quick test to verify Nova fixes are working.
"""

import os
import sys

def test_nova_quick():
    """Quick test of Nova with fixes."""
    print("🚀 Quick Nova Test")
    print("=" * 30)
    
    try:
        # Test model wrapper
        print("🤖 Testing NovaModelWrapper...")
        from nova_model_integration import NovaModelWrapper
        
        model_wrapper = NovaModelWrapper(
            model_path="output_expanded/nova_model_expanded_epoch_17.pt",
            tokenizer_path="output_bpe/nova_bpe_tokenizer.json",
            use_memory_optimization=True
        )
        print("✅ Model wrapper created successfully")
        
        # Test generation
        print("🧠 Testing text generation...")
        response = model_wrapper.generate_text("Hello, how are you?")
        print(f"✅ Response: '{response}'")
        
        print("\n🎉 Quick test PASSED!")
        print("✅ Nova fixes are working!")
        return True
        
    except Exception as e:
        print(f"❌ Quick test FAILED: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_nova_quick()
    if success:
        print("\n🎯 Nova is ready to use!")
        print("Try: python enhanced_nova_cli.py")
    else:
        print("\n🔧 There are still issues to fix.")
    
    input("Press Enter to continue...")
