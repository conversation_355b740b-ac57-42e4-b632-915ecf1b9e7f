"""
Test simple text generation to isolate the issue.
"""

import sys
import os
import time
import torch

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_simple_generation():
    """Test simple text generation without the full Nova system."""
    try:
        print("🧪 Testing simple text generation...")
        
        # Import just the model wrapper
        from nova_model_integration import NovaModelWrapper
        
        print("✅ Creating model wrapper...")
        wrapper = NovaModelWrapper(
            model_path="output_expanded/nova_model_expanded_epoch_17.pt",
            tokenizer_path="output_bpe/nova_bpe_tokenizer.json",
            use_memory_optimization=True
        )
        print("✅ Model wrapper created")
        
        # Test simple generation
        print("\n🗣️ Testing simple generation...")
        test_prompt = "User: Hello, how are you? <eos> Nova:"
        
        print(f"Prompt: '{test_prompt}'")
        
        start_time = time.time()
        response = wrapper.generate_text(test_prompt)
        end_time = time.time()
        
        print(f"✅ Response generated in {end_time - start_time:.2f}s")
        print(f"📝 Response: '{response}'")
        
        # Test fallback response
        print("\n🔄 Testing fallback response...")
        fallback = wrapper._get_fallback_response("Hello, how are you?")
        print(f"📝 Fallback: '{fallback}'")
        
        return True
        
    except Exception as e:
        print(f"\n❌ ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_simple_generation()
    if success:
        print("\n🎉 Simple generation test successful!")
        print("The parameter fix is working correctly.")
    else:
        print("\n❌ Simple generation test failed.")
    
    input("\nPress Enter to continue...")
