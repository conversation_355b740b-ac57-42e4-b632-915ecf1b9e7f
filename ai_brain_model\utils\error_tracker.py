"""
Enhanced Error Tracking and Debugging Utilities for Nova AI.

This module provides comprehensive error tracking, detailed logging,
and debugging utilities to help identify and resolve issues in Nova.
"""

import os
import sys
import time
import traceback
import logging
import json
from datetime import datetime
from typing import Dict, Any, Optional, List, Callable
from functools import wraps

# Get logger
logger = logging.getLogger(__name__)

class ErrorTracker:
    """
    Comprehensive error tracking system for Nova AI.
    """
    
    def __init__(self, log_dir: str = "logs"):
        """
        Initialize the error tracker.
        
        Args:
            log_dir: Directory to store error logs
        """
        self.log_dir = log_dir
        self.error_log_path = os.path.join(log_dir, "nova_detailed_errors.jsonl")
        self.session_errors = []
        
        # Ensure log directory exists
        os.makedirs(log_dir, exist_ok=True)
        
    def log_error(self, 
                  error: Exception, 
                  context: str = "", 
                  user_input: str = "", 
                  additional_info: Optional[Dict[str, Any]] = None) -> str:
        """
        Log a detailed error with full context.
        
        Args:
            error: The exception that occurred
            context: Context where the error occurred
            user_input: User input that triggered the error
            additional_info: Additional debugging information
            
        Returns:
            Error ID for tracking
        """
        error_id = f"ERR_{int(time.time())}_{len(self.session_errors)}"
        
        error_details = {
            "error_id": error_id,
            "timestamp": datetime.now().isoformat(),
            "error_type": type(error).__name__,
            "error_message": str(error),
            "context": context,
            "user_input": user_input,
            "traceback": traceback.format_exc(),
            "system_info": {
                "python_version": sys.version,
                "platform": sys.platform,
                "working_directory": os.getcwd()
            },
            "additional_info": additional_info or {}
        }
        
        # Add to session errors
        self.session_errors.append(error_details)
        
        # Write to file
        try:
            with open(self.error_log_path, "a", encoding="utf-8") as f:
                f.write(json.dumps(error_details) + "\n")
        except Exception as e:
            logger.error(f"Failed to write error to file: {e}")
        
        # Log to standard logger
        logger.error(f"Error {error_id}: {error_details['error_message']}")
        logger.error(f"Context: {context}")
        if user_input:
            logger.error(f"User input: {user_input}")
        logger.debug(f"Full traceback: {error_details['traceback']}")
        
        return error_id
    
    def get_recent_errors(self, count: int = 10) -> List[Dict[str, Any]]:
        """
        Get recent errors from the current session.
        
        Args:
            count: Number of recent errors to return
            
        Returns:
            List of recent error details
        """
        return self.session_errors[-count:]
    
    def get_error_summary(self) -> Dict[str, Any]:
        """
        Get a summary of errors in the current session.
        
        Returns:
            Error summary statistics
        """
        if not self.session_errors:
            return {"total_errors": 0, "error_types": {}}
        
        error_types = {}
        for error in self.session_errors:
            error_type = error["error_type"]
            error_types[error_type] = error_types.get(error_type, 0) + 1
        
        return {
            "total_errors": len(self.session_errors),
            "error_types": error_types,
            "first_error_time": self.session_errors[0]["timestamp"],
            "last_error_time": self.session_errors[-1]["timestamp"]
        }

# Global error tracker instance
_error_tracker = ErrorTracker()

def track_errors(context: str = ""):
    """
    Decorator to automatically track errors in functions.
    
    Args:
        context: Context description for the function
        
    Returns:
        Decorated function
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                error_context = context or f"{func.__module__}.{func.__name__}"
                error_id = _error_tracker.log_error(
                    error=e,
                    context=error_context,
                    additional_info={
                        "function": func.__name__,
                        "module": func.__module__,
                        "args": str(args)[:200],  # Limit length
                        "kwargs": str(kwargs)[:200]  # Limit length
                    }
                )
                logger.error(f"Function {func.__name__} failed with error {error_id}")
                raise  # Re-raise the exception
        return wrapper
    return decorator

def log_user_interaction_error(error: Exception, user_input: str, context: str = "user_interaction") -> str:
    """
    Log an error that occurred during user interaction.
    
    Args:
        error: The exception that occurred
        user_input: The user's input that caused the error
        context: Additional context
        
    Returns:
        Error ID for tracking
    """
    return _error_tracker.log_error(
        error=error,
        context=context,
        user_input=user_input,
        additional_info={
            "interaction_type": "user_message_processing"
        }
    )

def log_model_error(error: Exception, prompt: str = "", context: str = "model_generation") -> str:
    """
    Log an error that occurred during model generation.
    
    Args:
        error: The exception that occurred
        prompt: The prompt that caused the error
        context: Additional context
        
    Returns:
        Error ID for tracking
    """
    return _error_tracker.log_error(
        error=error,
        context=context,
        user_input=prompt,
        additional_info={
            "error_type": "model_generation",
            "prompt_length": len(prompt) if prompt else 0
        }
    )

def log_system_error(error: Exception, context: str = "system") -> str:
    """
    Log a system-level error.
    
    Args:
        error: The exception that occurred
        context: Additional context
        
    Returns:
        Error ID for tracking
    """
    return _error_tracker.log_error(
        error=error,
        context=context,
        additional_info={
            "error_type": "system_error"
        }
    )

def get_error_summary() -> Dict[str, Any]:
    """
    Get a summary of all errors in the current session.
    
    Returns:
        Error summary
    """
    return _error_tracker.get_error_summary()

def get_recent_errors(count: int = 5) -> List[Dict[str, Any]]:
    """
    Get recent errors from the current session.
    
    Args:
        count: Number of recent errors to return
        
    Returns:
        List of recent error details
    """
    return _error_tracker.get_recent_errors(count)

def print_error_summary():
    """
    Print a formatted error summary to the console.
    """
    summary = get_error_summary()
    
    if summary["total_errors"] == 0:
        print("✅ No errors recorded in this session.")
        return
    
    print(f"\n🚨 ERROR SUMMARY - {summary['total_errors']} errors recorded:")
    print("=" * 50)
    
    for error_type, count in summary["error_types"].items():
        print(f"  {error_type}: {count}")
    
    print(f"\nFirst error: {summary['first_error_time']}")
    print(f"Last error:  {summary['last_error_time']}")
    
    print(f"\nDetailed errors logged to: {_error_tracker.error_log_path}")
    print("=" * 50)

def setup_enhanced_error_handling():
    """
    Set up enhanced error handling for the entire application.
    """
    # Set up exception hook to catch unhandled exceptions
    def exception_handler(exc_type, exc_value, exc_traceback):
        if issubclass(exc_type, KeyboardInterrupt):
            # Allow keyboard interrupts to work normally
            sys.__excepthook__(exc_type, exc_value, exc_traceback)
            return
        
        # Log the unhandled exception
        error_id = _error_tracker.log_error(
            error=exc_value,
            context="unhandled_exception",
            additional_info={
                "exception_type": exc_type.__name__,
                "is_unhandled": True
            }
        )
        
        print(f"\n🚨 UNHANDLED EXCEPTION - Error ID: {error_id}")
        print(f"Type: {exc_type.__name__}")
        print(f"Message: {exc_value}")
        print(f"See {_error_tracker.error_log_path} for full details.")
        
        # Call the default exception handler
        sys.__excepthook__(exc_type, exc_value, exc_traceback)
    
    # Install the exception handler
    sys.excepthook = exception_handler
    
    logger.info("Enhanced error handling enabled")
