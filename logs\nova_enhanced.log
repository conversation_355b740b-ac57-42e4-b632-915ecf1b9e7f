2025-05-30 21:32:47 - root - INFO - [logging_config.py:173] - setup_logging() - Logging configured with level=debug, log_to_console=True, log_to_file=True
2025-05-30 21:32:47 - root - DEBUG - [logging_config.py:175] - setup_logging() - Debug mode enabled - detailed logging active
2025-05-30 21:32:48 - root - DEBUG - [logging_config.py:181] - setup_logging() - System: Windows 10
2025-05-30 21:32:48 - root - DEBUG - [logging_config.py:182] - setup_logging() - Python: 3.11.9 (tags/v3.11.9:de54cf5, Apr  2 2024, 10:12:12) [MSC v.1938 64 bit (AMD64)]
2025-05-30 21:32:48 - root - DEBUG - [logging_config.py:183] - setup_logging() - Working directory: C:\Users\<USER>\Desktop\AI Brain
2025-05-30 21:32:48 - root - DEBUG - [logging_config.py:184] - setup_logging() - Log directory: logs
2025-05-30 21:32:48 - root - DEBUG - [logging_config.py:186] - setup_logging() - Main log file: logs\nova_enhanced.log
2025-05-30 21:32:48 - root - DEBUG - [logging_config.py:187] - setup_logging() - Error log file: logs\nova_errors.log
2025-05-30 21:32:48 - ai_brain_model.utils.error_tracker - INFO - [error_tracker.py:300] - setup_enhanced_error_handling() - Enhanced error handling enabled
2025-05-30 21:32:48 - __main__ - INFO - [enhanced_nova_cli.py:276] - main() - 🚀 Starting Enhanced Nova CLI...
2025-05-30 21:32:48 - __main__ - INFO - [enhanced_nova_cli.py:277] - main() - 📁 Model: output_expanded/nova_model_expanded_epoch_17.pt
2025-05-30 21:32:48 - __main__ - INFO - [enhanced_nova_cli.py:278] - main() - 📁 Tokenizer: output_bpe/nova_bpe_tokenizer.json
2025-05-30 21:32:48 - __main__ - INFO - [enhanced_nova_cli.py:279] - main() - 📁 Knowledge Base: nova_knowledge_base.db
2025-05-30 21:32:48 - __main__ - INFO - [enhanced_nova_cli.py:52] - __init__() - 🚀 Initializing Enhanced Nova Interface...
2025-05-30 21:32:48 - __main__ - INFO - [enhanced_nova_cli.py:68] - initialize_nova() - 🔧 Starting Nova initialization...
2025-05-30 21:32:48 - __main__ - DEBUG - [enhanced_nova_cli.py:108] - _verify_files() - 🔍 Verifying required files...
2025-05-30 21:32:48 - __main__ - DEBUG - [enhanced_nova_cli.py:122] - _verify_files() - ✅ Found Model: output_expanded/nova_model_expanded_epoch_17.pt
2025-05-30 21:32:48 - __main__ - DEBUG - [enhanced_nova_cli.py:122] - _verify_files() - ✅ Found Tokenizer: output_bpe/nova_bpe_tokenizer.json
2025-05-30 21:32:48 - __main__ - DEBUG - [enhanced_nova_cli.py:122] - _verify_files() - ✅ Found Knowledge Base: nova_knowledge_base.db
2025-05-30 21:32:49 - __main__ - DEBUG - [enhanced_nova_cli.py:75] - initialize_nova() - 📦 Importing Nova components...
