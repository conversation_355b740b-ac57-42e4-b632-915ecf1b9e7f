"""
Logging configuration for Nova AI.

This module provides a centralized logging configuration for Nova AI,
with support for different log levels, log rotation, and structured logging.
"""

import os
import logging
import logging.handlers
import json
from datetime import datetime
from typing import Dict, Any, Optional, Union

# Default log directory
DEFAULT_LOG_DIR = "logs"

# Log levels
LOG_LEVELS = {
    "debug": logging.DEBUG,
    "info": logging.INFO,
    "warning": logging.WARNING,
    "error": logging.ERROR,
    "critical": logging.CRITICAL
}

class StructuredLogFormatter(logging.Formatter):
    """
    A formatter that outputs logs in a structured JSON format.
    """

    def format(self, record):
        """Format the log record as JSON."""
        # We don't use the standard formatter's output, but call it to ensure
        # any formatTime or formatException methods are properly called
        super().format(record)

        # Create a structured log entry
        log_entry = {
            "timestamp": datetime.fromtimestamp(record.created).isoformat(),
            "level": record.levelname,
            "logger": record.name,
            "message": record.getMessage(),
            "module": record.module,
            "function": record.funcName,
            "line": record.lineno
        }

        # Add exception info if available
        if record.exc_info:
            log_entry["exception"] = {
                "type": record.exc_info[0].__name__,
                "message": str(record.exc_info[1]),
                "traceback": self.formatException(record.exc_info)
            }

        # Add extra fields if available
        if hasattr(record, "extra"):
            log_entry["extra"] = record.extra

        return json.dumps(log_entry)

def setup_logging(
    log_level: Union[str, int] = "info",
    log_dir: str = DEFAULT_LOG_DIR,
    log_to_console: bool = True,
    log_to_file: bool = True,
    log_file_name: str = "nova.log",
    max_log_size: int = 10 * 1024 * 1024,  # 10 MB
    backup_count: int = 5,
    structured_logging: bool = False,
    enable_debug_mode: bool = False
) -> None:
    """
    Set up logging for Nova AI.

    Args:
        log_level: The log level (debug, info, warning, error, critical)
        log_dir: The directory to store log files
        log_to_console: Whether to log to the console
        log_to_file: Whether to log to a file
        log_file_name: The name of the log file
        max_log_size: The maximum size of the log file before rotation
        backup_count: The number of backup log files to keep
        structured_logging: Whether to use structured logging (JSON format)
        enable_debug_mode: Whether to enable enhanced debug logging
    """
    # Create the log directory if it doesn't exist
    if log_to_file and not os.path.exists(log_dir):
        os.makedirs(log_dir)

    # Get the log level
    if isinstance(log_level, str):
        level = LOG_LEVELS.get(log_level.lower(), logging.INFO)
    elif isinstance(log_level, int):
        level = log_level
    else:
        level = logging.INFO

    # Enable debug mode if requested or if log level is debug
    if enable_debug_mode or level == logging.DEBUG:
        level = logging.DEBUG

    # Configure the root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(level)

    # Remove existing handlers
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)

    # Create formatters
    if structured_logging:
        formatter = StructuredLogFormatter()
    else:
        # Enhanced formatter with more details for debugging
        if level == logging.DEBUG:
            formatter = logging.Formatter(
                "%(asctime)s - %(name)s - %(levelname)s - [%(filename)s:%(lineno)d] - %(funcName)s() - %(message)s",
                datefmt="%Y-%m-%d %H:%M:%S"
            )
        else:
            formatter = logging.Formatter(
                "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
                datefmt="%Y-%m-%d %H:%M:%S"
            )

    # Add console handler if requested
    if log_to_console:
        import sys
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(level)
        console_handler.setFormatter(formatter)
        root_logger.addHandler(console_handler)

    # Add file handler if requested
    if log_to_file:
        log_file_path = os.path.join(log_dir, log_file_name)
        try:
            file_handler = logging.handlers.RotatingFileHandler(
                log_file_path,
                maxBytes=max_log_size,
                backupCount=backup_count,
                encoding='utf-8'
            )
            file_handler.setLevel(level)
            file_handler.setFormatter(formatter)
            root_logger.addHandler(file_handler)
        except Exception as e:
            print(f"Warning: Could not create file handler for {log_file_path}: {e}")
            print("Continuing with console logging only...")

    # Add separate error log file for critical issues
    if log_to_file:
        error_log_path = os.path.join(log_dir, "nova_errors.log")
        try:
            error_handler = logging.handlers.RotatingFileHandler(
                error_log_path,
                maxBytes=max_log_size,
                backupCount=backup_count,
                encoding='utf-8'
            )
            error_handler.setLevel(logging.ERROR)
            error_handler.setFormatter(logging.Formatter(
                "%(asctime)s - %(name)s - %(levelname)s - [%(filename)s:%(lineno)d] - %(funcName)s() - %(message)s",
                datefmt="%Y-%m-%d %H:%M:%S"
            ))
            root_logger.addHandler(error_handler)
        except Exception as e:
            print(f"Warning: Could not create error handler for {error_log_path}: {e}")

    # Log the configuration
    logging.info(f"Logging configured with level={log_level}, log_to_console={log_to_console}, log_to_file={log_to_file}")
    if level == logging.DEBUG:
        logging.debug("Debug mode enabled - detailed logging active")

    # Log system information for debugging
    if level == logging.DEBUG:
        import platform
        import sys
        logging.debug(f"System: {platform.system()} {platform.release()}")
        logging.debug(f"Python: {sys.version}")
        logging.debug(f"Working directory: {os.getcwd()}")
        logging.debug(f"Log directory: {log_dir}")
        if log_to_file:
            logging.debug(f"Main log file: {os.path.join(log_dir, log_file_name)}")
            logging.debug(f"Error log file: {os.path.join(log_dir, 'nova_errors.log')}")

def get_logger(name: str) -> logging.Logger:
    """
    Get a logger with the given name.

    Args:
        name: The name of the logger

    Returns:
        A logger instance
    """
    return logging.getLogger(name)

def log_with_context(
    logger: logging.Logger,
    level: str,
    message: str,
    extra: Optional[Dict[str, Any]] = None
) -> None:
    """
    Log a message with additional context.

    Args:
        logger: The logger to use
        level: The log level (debug, info, warning, error, critical)
        message: The log message
        extra: Additional context to include in the log
    """
    log_method = getattr(logger, level.lower())

    # Create a LogRecord with extra context
    if extra:
        log_method(message, extra={"extra": extra})
    else:
        log_method(message)

# Alias for setup_logging for backward compatibility
def configure_logging(
    log_level: Union[str, int] = "info",
    log_dir: str = DEFAULT_LOG_DIR,
    log_to_console: bool = True,
    log_to_file: bool = True,
    log_file: str = "nova.log",
    max_log_size: int = 10 * 1024 * 1024,  # 10 MB
    backup_count: int = 5,
    structured_logging: bool = False,
    enable_debug_mode: bool = False
) -> None:
    """
    Configure logging for Nova AI (alias for setup_logging).

    Args:
        log_level: The log level (debug, info, warning, error, critical)
        log_dir: The directory to store log files
        log_to_console: Whether to log to the console
        log_to_file: Whether to log to a file
        log_file: The name of the log file
        max_log_size: The maximum size of the log file before rotation
        backup_count: The number of backup log files to keep
        structured_logging: Whether to use structured logging (JSON format)
        enable_debug_mode: Whether to enable enhanced debug logging
    """
    setup_logging(
        log_level=log_level,
        log_dir=log_dir,
        log_to_console=log_to_console,
        log_to_file=log_to_file,
        log_file_name=log_file,
        max_log_size=max_log_size,
        backup_count=backup_count,
        structured_logging=structured_logging,
        enable_debug_mode=enable_debug_mode
    )

# Set up logging when the module is imported
setup_logging()
