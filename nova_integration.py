"""
Nova AI Integration

This script integrates the knowledge base with the model function and handles user messages.
It provides a production-ready implementation of the conversation handler.
"""

import os
import sys
import time
import json
import logging
from typing import Dict, List, Any, Optional, Callable, Union

# Add the project directory to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from ai_brain_model.knowledge_base.knowledge_base import KnowledgeBase
from ai_brain_model.conversation.conversation_handler import Convers<PERSON><PERSON><PERSON><PERSON>
from ai_brain_model.conversation.conversation_manager import ConversationState
from ai_brain_model.utils.tools.tool_registry import get_all_tools
from ai_brain_model.utils.logging_config import get_logger, configure_logging

# Configure logging
configure_logging(log_level="info", log_file="nova_integration.log")
logger = get_logger(__name__)

class NovaAI:
    """
    Main class for Nova AI integration.

    This class provides a production-ready implementation of Nova AI with:
    - Knowledge base integration
    - Model function integration
    - Conversation handling
    - Tool integration
    - Conversation logging and analytics
    """

    def __init__(self, model_function: Callable[[str], str],
                knowledge_base_path: str = "nova_knowledge_base.db",
                conversation_log_path: str = "conversations",
                enable_tools: bool = True):
        """
        Initialize Nova AI.

        Args:
            model_function: Function that generates text from a prompt
            knowledge_base_path: Path to the knowledge base
            conversation_log_path: Path to save conversation logs
            enable_tools: Whether to enable tools
        """
        self.model_function = model_function
        self.knowledge_base_path = knowledge_base_path
        self.conversation_log_path = conversation_log_path
        self.enable_tools = enable_tools

        # Create directories if they don't exist
        os.makedirs(self.conversation_log_path, exist_ok=True)

        # Initialize knowledge base
        logger.info(f"Initializing knowledge base from {knowledge_base_path}")
        self.kb = KnowledgeBase(db_path=knowledge_base_path)

        # Initialize tools if enabled
        self.tools = {}
        if self.enable_tools:
            logger.info("Initializing tools")
            self._initialize_tools()

        # Initialize conversation handler
        logger.info("Initializing conversation handler")
        self.conversation_handler = ConversationHandler(
            knowledge_base=self.kb,
            model_generate_func=self.model_function,
            available_tools=self.tools
        )

        # Session data
        self.session_id = None
        self.user_id = None
        self.session_start_time = None

        logger.info("Nova AI initialized successfully")

    def _initialize_tools(self) -> None:
        """Initialize tools for Nova AI."""
        try:
            # Get all available tools
            all_tools = get_all_tools()

            # Convert to the format expected by the conversation handler
            for tool in all_tools:
                self.tools[tool.name] = {
                    "name": tool.name,
                    "description": tool.description,
                    "execute": tool.execute
                }

            logger.info(f"Initialized {len(self.tools)} tools")
        except Exception as e:
            logger.error(f"Error initializing tools: {str(e)}")
            logger.warning("Continuing without tools")
            self.tools = {}

    def start_session(self, user_id: str = "anonymous") -> str:
        """
        Start a new conversation session.

        Args:
            user_id: Identifier for the user

        Returns:
            Session ID
        """
        # Generate a session ID
        import uuid
        self.session_id = str(uuid.uuid4())
        self.user_id = user_id
        self.session_start_time = time.time()

        logger.info(f"Started new session {self.session_id} for user {user_id}")
        return self.session_id

    def process_message(self, message: str) -> Dict[str, Any]:
        """
        Process a user message and generate a response.

        Args:
            message: The user's message

        Returns:
            A dictionary containing the response and metadata
        """
        logger.info(f"🔄 Processing message: '{message[:50]}...'")

        if not self.session_id:
            logger.debug("🆔 No session ID, starting new session...")
            self.start_session()

        start_time = time.time()
        logger.debug(f"⏱️ Processing started at {start_time}")

        try:
            # Process the message
            logger.info("🧠 Calling conversation handler...")
            response = self.conversation_handler.handle_message(message)
            logger.info(f"✅ Conversation handler returned: '{response[:50]}...'")

            # Get conversation state
            logger.debug("📊 Getting conversation context...")
            context = self.conversation_handler.conversation_manager.get_relevant_context(message)
            current_state = context.get("current_state", ConversationState.MAIN_TOPIC)
            logger.debug(f"📊 Current state: {current_state}")

            # Get stats
            logger.debug("📈 Getting conversation stats...")
            stats = self.conversation_handler.get_stats()
            logger.debug(f"📈 Stats: {stats}")

            # Calculate processing time
            processing_time = time.time() - start_time
            logger.debug(f"⏱️ Processing time: {processing_time:.2f}s")

            # Create result
            logger.debug("📦 Creating result dictionary...")
            result = {
                "response": response,
                "session_id": self.session_id,
                "user_id": self.user_id,
                "timestamp": time.time(),
                "processing_time": processing_time,
                "conversation_state": current_state,
                "turn_count": stats.get("total_turns", 0),
                "regeneration_count": stats.get("total_regenerations", 0)
            }

            # Log the interaction
            logger.debug("📝 Logging interaction...")
            self._log_interaction(message, result)

            logger.info(f"✅ Processed message in {processing_time:.2f}s (Session: {self.session_id}, State: {current_state})")
            logger.info(f"🎯 Final response: '{response[:100]}...'")
            return result

        except Exception as e:
            logger.error(f"❌ Error processing message: {str(e)}", exc_info=True)

            # Return an error response
            error_result = {
                "response": "I'm sorry, I encountered an error while processing your message. Please try again.",
                "session_id": self.session_id,
                "user_id": self.user_id,
                "timestamp": time.time(),
                "processing_time": time.time() - start_time,
                "error": str(e)
            }

            # Log the error
            self._log_interaction(message, error_result, is_error=True)

            return error_result

    def _log_interaction(self, message: str, result: Dict[str, Any], is_error: bool = False) -> None:
        """
        Log an interaction to a file.

        Args:
            message: The user's message
            result: The result dictionary
            is_error: Whether this is an error log
        """
        try:
            # Create the log entry
            log_entry = {
                "user_message": message,
                "result": result,
                "timestamp": time.time(),
                "is_error": is_error
            }

            # Determine the log file path
            log_file = os.path.join(
                self.conversation_log_path,
                f"session_{self.session_id}.jsonl"
            )

            # Append to the log file
            with open(log_file, "a") as f:
                f.write(json.dumps(log_entry) + "\n")

        except Exception as e:
            logger.error(f"Error logging interaction: {str(e)}")

    def end_session(self) -> Dict[str, Any]:
        """
        End the current session and return session statistics.

        Returns:
            Session statistics
        """
        if not self.session_id:
            logger.warning("No active session to end")
            return {"error": "No active session"}

        # Get session duration
        session_duration = time.time() - self.session_start_time

        # Get conversation stats
        stats = self.conversation_handler.get_stats()

        # Save the conversation
        conversation_file = os.path.join(
            self.conversation_log_path,
            f"conversation_{self.session_id}.json"
        )
        self.conversation_handler.save_conversation(conversation_file)

        # Create session summary
        session_summary = {
            "session_id": self.session_id,
            "user_id": self.user_id,
            "start_time": self.session_start_time,
            "end_time": time.time(),
            "duration": session_duration,
            "turn_count": stats.get("total_turns", 0),
            "regeneration_count": stats.get("total_regenerations", 0),
            "average_response_time": stats.get("average_response_time", 0),
            "tool_usage_count": stats.get("tool_usage_count", 0),
            "conversation_file": conversation_file
        }

        # Log session end
        logger.info(f"Ended session {self.session_id} after {session_duration:.2f}s with {stats.get('total_turns', 0)} turns")

        # Reset session data
        self.session_id = None
        self.user_id = None
        self.session_start_time = None

        return session_summary

    def get_session_info(self) -> Dict[str, Any]:
        """
        Get information about the current session.

        Returns:
            Session information
        """
        if not self.session_id:
            return {"error": "No active session"}

        # Get session duration
        session_duration = time.time() - self.session_start_time

        # Get conversation stats
        stats = self.conversation_handler.get_stats()

        # Create session info
        session_info = {
            "session_id": self.session_id,
            "user_id": self.user_id,
            "start_time": self.session_start_time,
            "current_time": time.time(),
            "duration": session_duration,
            "turn_count": stats.get("total_turns", 0),
            "regeneration_count": stats.get("total_regenerations", 0),
            "average_response_time": stats.get("average_response_time", 0),
            "tool_usage_count": stats.get("tool_usage_count", 0)
        }

        return session_info

# Example model function that would be replaced with your actual model
def example_model_function(prompt: str) -> str:
    """
    Example model function that would be replaced with your actual model.

    Args:
        prompt: The prompt to generate text from

    Returns:
        The generated text
    """
    # This is just a placeholder - replace with your actual model
    return "This is a placeholder response. Replace this function with your actual model."

# Example usage
if __name__ == "__main__":
    # This is just an example - in production, you would import this module
    # and use the NovaAI class with your actual model function

    print("This is a module for integration. Import and use the NovaAI class in your application.")
