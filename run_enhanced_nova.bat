@echo off
echo ===================================
echo   NOVA AI - Enhanced Error Tracking
echo ===================================
echo.

echo Activating virtual environment...
call venv\Scripts\activate.bat

if %errorlevel% neq 0 (
    echo ERROR: Failed to activate virtual environment.
    echo Make sure the virtual environment exists in the 'venv' folder.
    pause
    exit /b 1
)

echo.
echo Virtual environment activated successfully!
echo.

echo Checking for required files...
if not exist "output_expanded\nova_model_expanded_epoch_17.pt" (
    echo WARNING: Model file not found at output_expanded\nova_model_expanded_epoch_17.pt
    echo Please ensure the model file exists.
)

if not exist "output_bpe\nova_bpe_tokenizer.json" (
    echo WARNING: Tokenizer file not found at output_bpe\nova_bpe_tokenizer.json
    echo Please ensure the tokenizer file exists.
)

if not exist "nova_knowledge_base.db" (
    echo WARNING: Knowledge base not found at nova_knowledge_base.db
    echo Creating knowledge base...
    python populate_knowledge_base.py
)

echo.
echo ===================================
echo   STARTING ENHANCED NOVA AI
echo ===================================
echo.
echo Features:
echo - Comprehensive error tracking
echo - Detailed debug logging
echo - Enhanced error recovery
echo - Real-time error reporting
echo.
echo Commands during chat:
echo - 'errors' - Show recent errors
echo - 'debug' - Toggle debug mode
echo - 'help' - Show help
echo - 'exit' - End session
echo.

python enhanced_nova_cli.py

echo.
echo ===================================
echo   SESSION ENDED
echo ===================================
echo.
echo Check these files for detailed logs:
echo - logs\nova_enhanced.log (main log)
echo - logs\nova_errors.log (error-only log)
echo - logs\nova_detailed_errors.jsonl (structured errors)
echo.

pause
