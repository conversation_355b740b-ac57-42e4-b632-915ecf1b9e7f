"""
Test the fix directly by running Nova AI with our greeting.
"""

import sys
import os
import time

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_nova_greeting():
    """Test Nova AI with a greeting to see if it works."""
    try:
        print("🧪 Testing Nova AI with greeting...")
        print("=" * 50)
        
        # Import the modules
        from nova_model_integration import NovaModelWrapper
        from nova_integration import NovaAI
        
        print("✅ Modules imported successfully")
        
        # Create model wrapper
        print("Creating model wrapper...")
        model_wrapper = NovaModelWrapper(
            model_path="output_expanded/nova_model_expanded_epoch_17.pt",
            tokenizer_path="output_bpe/nova_bpe_tokenizer.json",
            use_memory_optimization=True
        )
        print("✅ Model wrapper created")
        
        # Create Nova AI
        print("Creating Nova AI...")
        nova = NovaAI(
            model_function=model_wrapper.generate_text,
            knowledge_base_path="nova_knowledge_base.db",
            enable_tools=True
        )
        print("✅ Nova AI created")
        
        # Start session
        print("Starting session...")
        session_id = nova.start_session()
        print(f"✅ Session started: {session_id}")
        
        # Test the exact greeting that was failing
        print("\n🗣️ Testing greeting: 'Hi, how are you doing?'")
        start_time = time.time()
        
        result = nova.process_message("Hi, how are you doing?")
        
        end_time = time.time()
        
        print(f"✅ Response received in {end_time - start_time:.2f}s")
        print(f"📝 Nova's response: '{result['response']}'")
        
        # End session
        nova.end_session()
        print("✅ Session ended successfully")
        
        print("\n🎉 SUCCESS! Nova AI is working correctly!")
        return True
        
    except Exception as e:
        print(f"\n❌ ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_nova_greeting()
    if success:
        print("\n✅ The fix is working! Nova AI should now work properly.")
    else:
        print("\n❌ The fix is not working. There may be other issues.")
    
    input("\nPress Enter to continue...")
