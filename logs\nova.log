2025-05-20 16:17:28 - root - INFO - Logging configured with level=info, log_to_console=True, log_to_file=True
2025-05-20 16:17:28 - ai_brain_model.knowledge_base.vector_store - INFO - Initialized vector store with dimension 768
2025-05-20 16:17:28 - ai_brain_model.knowledge_base.vector_store - INFO - Vector store contains 0 vectors
2025-05-20 16:18:51 - ai_brain_model.knowledge_base.entity_extractor - WARNING - spaCy model en_core_web_md not found. Falling back to rule-based extraction.
2025-05-20 16:18:51 - ai_brain_model.knowledge_base.entity_extractor - INFO - Initialized entity extractor (use_spacy=True)
2025-05-20 16:18:55 - ai_brain_model.knowledge_base.knowledge_base - INFO - Database initialized
2025-05-20 16:18:55 - ai_brain_model.knowledge_base.knowledge_base - INFO - Initialized knowledge base with database at nova_knowledge_base.db
2025-05-20 16:18:55 - ai_brain_model.knowledge_base.knowledge_base - INFO - Vector dimension: 768, Using spaCy: True, spaCy model: en_core_web_md
2025-05-20 16:18:55 - __main__ - INFO - Populating knowledge base with base conversation knowledge...
2025-05-20 16:18:55 - ai_brain_model.conversation.conversation_knowledge - INFO - Populating knowledge base with conversation knowledge...
2025-05-20 16:18:55 - ai_brain_model.knowledge_base.vector_store - INFO - Saved 1 vectors to vector_store.pkl
2025-05-20 16:18:56 - ai_brain_model.knowledge_base.knowledge_base - INFO - Added knowledge with ID 1 and 0 entities
2025-05-20 16:18:56 - ai_brain_model.conversation.conversation_knowledge - INFO - Added conversation strategy: When someone shares a problem, first acknowledge t...
2025-05-20 16:18:56 - ai_brain_model.knowledge_base.vector_store - INFO - Saved 2 vectors to vector_store.pkl
2025-05-20 16:18:56 - ai_brain_model.knowledge_base.knowledge_base - ERROR - Error adding knowledge: attempt to write a readonly database
2025-05-20 16:18:56 - ai_brain_model.conversation.conversation_knowledge - ERROR - Error adding conversation strategy: attempt to write a readonly database
2025-05-20 16:18:56 - ai_brain_model.knowledge_base.vector_store - INFO - Saved 3 vectors to vector_store.pkl
2025-05-20 16:18:57 - ai_brain_model.knowledge_base.knowledge_base - INFO - Added knowledge with ID 2 and 0 entities
2025-05-20 16:18:57 - ai_brain_model.conversation.conversation_knowledge - INFO - Added conversation strategy: Summarize what the person has said to show you're ...
2025-05-20 16:18:57 - ai_brain_model.knowledge_base.vector_store - INFO - Saved 4 vectors to vector_store.pkl
2025-05-20 16:18:57 - ai_brain_model.knowledge_base.knowledge_base - INFO - Added knowledge with ID 3 and 0 entities
2025-05-20 16:18:57 - ai_brain_model.conversation.conversation_knowledge - INFO - Added conversation strategy: When changing topics, use a transitional phrase to...
2025-05-20 16:18:57 - ai_brain_model.knowledge_base.vector_store - INFO - Saved 5 vectors to vector_store.pkl
2025-05-20 16:18:57 - ai_brain_model.knowledge_base.knowledge_base - INFO - Added knowledge with ID 4 and 0 entities
2025-05-20 16:18:57 - ai_brain_model.conversation.conversation_knowledge - INFO - Added conversation strategy: If you don't know the answer to a question, be hon...
2025-05-20 16:18:57 - ai_brain_model.knowledge_base.vector_store - INFO - Saved 6 vectors to vector_store.pkl
2025-05-20 16:18:57 - ai_brain_model.knowledge_base.knowledge_base - INFO - Added knowledge with ID 5 and 0 entities
2025-05-20 16:18:57 - ai_brain_model.conversation.conversation_knowledge - INFO - Added conversation strategy: Use the person's name occasionally to personalize ...
2025-05-20 16:18:58 - ai_brain_model.knowledge_base.vector_store - INFO - Saved 7 vectors to vector_store.pkl
2025-05-20 16:18:58 - ai_brain_model.knowledge_base.knowledge_base - INFO - Added knowledge with ID 6 and 0 entities
2025-05-20 16:18:59 - ai_brain_model.conversation.conversation_knowledge - INFO - Added conversation strategy: Match the person's communication style and tone to...
2025-05-20 16:18:59 - ai_brain_model.knowledge_base.vector_store - INFO - Saved 8 vectors to vector_store.pkl
2025-05-20 16:18:59 - ai_brain_model.knowledge_base.knowledge_base - INFO - Added knowledge with ID 7 and 0 entities
2025-05-20 16:18:59 - ai_brain_model.conversation.conversation_knowledge - INFO - Added conversation strategy: When someone is upset, validate their feelings eve...
2025-05-20 16:18:59 - ai_brain_model.knowledge_base.vector_store - INFO - Saved 9 vectors to vector_store.pkl
2025-05-20 16:18:59 - ai_brain_model.knowledge_base.knowledge_base - INFO - Added knowledge with ID 8 and 0 entities
2025-05-20 16:18:59 - ai_brain_model.conversation.conversation_knowledge - INFO - Added conversation strategy: Use humor appropriately to lighten the mood, but a...
2025-05-20 16:18:59 - ai_brain_model.knowledge_base.vector_store - INFO - Saved 10 vectors to vector_store.pkl
2025-05-20 16:19:00 - ai_brain_model.knowledge_base.knowledge_base - INFO - Added knowledge with ID 9 and 0 entities
2025-05-20 16:19:00 - ai_brain_model.conversation.conversation_knowledge - INFO - Added conversation strategy: If the conversation becomes tense, take a step bac...
2025-05-20 16:19:00 - ai_brain_model.conversation.conversation_knowledge - INFO - Added 9 conversation strategies to the knowledge base
2025-05-20 16:19:00 - ai_brain_model.knowledge_base.vector_store - INFO - Saved 11 vectors to vector_store.pkl
2025-05-20 16:19:00 - ai_brain_model.knowledge_base.knowledge_base - INFO - Added knowledge with ID 10 and 0 entities
2025-05-20 16:19:00 - ai_brain_model.conversation.conversation_knowledge - INFO - Added personality trait: Nova is friendly and approachable, using warm lang...
2025-05-20 16:19:00 - ai_brain_model.knowledge_base.vector_store - INFO - Saved 12 vectors to vector_store.pkl
2025-05-20 16:19:01 - ai_brain_model.knowledge_base.knowledge_base - INFO - Added knowledge with ID 11 and 0 entities
2025-05-20 16:19:01 - ai_brain_model.conversation.conversation_knowledge - INFO - Added personality trait: Nova is helpful and service-oriented, always looki...
2025-05-20 16:19:01 - ai_brain_model.knowledge_base.vector_store - INFO - Saved 13 vectors to vector_store.pkl
2025-05-20 16:19:01 - ai_brain_model.knowledge_base.knowledge_base - INFO - Added knowledge with ID 12 and 0 entities
2025-05-20 16:19:01 - ai_brain_model.conversation.conversation_knowledge - INFO - Added personality trait: Nova is empathetic, recognizing and acknowledging ...
2025-05-20 16:19:01 - ai_brain_model.knowledge_base.vector_store - INFO - Saved 14 vectors to vector_store.pkl
2025-05-20 16:19:01 - ai_brain_model.knowledge_base.knowledge_base - INFO - Added knowledge with ID 13 and 0 entities
2025-05-20 16:19:01 - ai_brain_model.conversation.conversation_knowledge - INFO - Added personality trait: Nova is knowledgeable but humble, sharing informat...
2025-05-20 16:19:01 - ai_brain_model.knowledge_base.vector_store - INFO - Saved 15 vectors to vector_store.pkl
2025-05-20 16:19:01 - ai_brain_model.knowledge_base.knowledge_base - INFO - Added knowledge with ID 14 and 0 entities
2025-05-20 16:19:01 - ai_brain_model.conversation.conversation_knowledge - INFO - Added personality trait: Nova is patient, never rushing the user or showing...
2025-05-20 16:19:01 - ai_brain_model.knowledge_base.vector_store - INFO - Saved 16 vectors to vector_store.pkl
2025-05-20 16:19:02 - ai_brain_model.knowledge_base.knowledge_base - INFO - Added knowledge with ID 15 and 0 entities
2025-05-20 16:19:02 - ai_brain_model.conversation.conversation_knowledge - INFO - Added personality trait: Nova is adaptable, adjusting her communication sty...
2025-05-20 16:19:02 - ai_brain_model.knowledge_base.vector_store - INFO - Saved 17 vectors to vector_store.pkl
2025-05-20 16:19:02 - ai_brain_model.knowledge_base.knowledge_base - INFO - Added knowledge with ID 16 and 0 entities
2025-05-20 16:19:02 - ai_brain_model.conversation.conversation_knowledge - INFO - Added personality trait: Nova is positive and optimistic, focusing on solut...
2025-05-20 16:19:02 - ai_brain_model.knowledge_base.vector_store - INFO - Saved 18 vectors to vector_store.pkl
2025-05-20 16:19:02 - ai_brain_model.knowledge_base.knowledge_base - INFO - Added knowledge with ID 17 and 0 entities
2025-05-20 16:19:02 - ai_brain_model.conversation.conversation_knowledge - INFO - Added personality trait: Nova is respectful of the user's time, providing c...
2025-05-20 16:19:02 - ai_brain_model.knowledge_base.vector_store - INFO - Saved 19 vectors to vector_store.pkl
2025-05-20 16:19:03 - ai_brain_model.knowledge_base.knowledge_base - INFO - Added knowledge with ID 18 and 0 entities
2025-05-20 16:19:03 - ai_brain_model.conversation.conversation_knowledge - INFO - Added personality trait: Nova has a sense of humor and can be playful when ...
2025-05-20 16:19:03 - ai_brain_model.knowledge_base.vector_store - INFO - Saved 20 vectors to vector_store.pkl
2025-05-20 16:19:04 - ai_brain_model.knowledge_base.knowledge_base - INFO - Added knowledge with ID 19 and 0 entities
2025-05-20 16:19:04 - ai_brain_model.conversation.conversation_knowledge - INFO - Added personality trait: Nova is honest and transparent, admitting when she...
2025-05-20 16:19:04 - ai_brain_model.conversation.conversation_knowledge - INFO - Added 10 personality traits to the knowledge base
2025-05-20 16:19:04 - ai_brain_model.knowledge_base.vector_store - INFO - Saved 21 vectors to vector_store.pkl
2025-05-20 16:19:05 - ai_brain_model.knowledge_base.knowledge_base - INFO - Added knowledge with ID 20 and 0 entities
2025-05-20 16:19:05 - ai_brain_model.conversation.conversation_knowledge - INFO - Added conversation pattern: Greeting -> Small talk -> Main topic -> Conclusion...
2025-05-20 16:19:05 - ai_brain_model.knowledge_base.vector_store - INFO - Saved 22 vectors to vector_store.pkl
2025-05-20 16:19:05 - ai_brain_model.knowledge_base.knowledge_base - INFO - Added knowledge with ID 21 and 0 entities
2025-05-20 16:19:05 - ai_brain_model.conversation.conversation_knowledge - INFO - Added conversation pattern: When greeting someone, acknowledge the time of day...
2025-05-20 16:19:05 - ai_brain_model.knowledge_base.vector_store - INFO - Saved 23 vectors to vector_store.pkl
2025-05-20 16:19:05 - ai_brain_model.knowledge_base.knowledge_base - INFO - Added knowledge with ID 22 and 0 entities
2025-05-20 16:19:05 - ai_brain_model.conversation.conversation_knowledge - INFO - Added conversation pattern: For small talk, discuss neutral topics like weathe...
2025-05-20 16:19:05 - ai_brain_model.knowledge_base.vector_store - INFO - Saved 24 vectors to vector_store.pkl
2025-05-20 16:19:06 - ai_brain_model.knowledge_base.knowledge_base - INFO - Added knowledge with ID 23 and 0 entities
2025-05-20 16:19:06 - ai_brain_model.conversation.conversation_knowledge - INFO - Added conversation pattern: When transitioning to the main topic, use phrases ...
2025-05-20 16:19:06 - ai_brain_model.knowledge_base.vector_store - INFO - Saved 25 vectors to vector_store.pkl
2025-05-20 16:19:06 - ai_brain_model.knowledge_base.knowledge_base - INFO - Added knowledge with ID 24 and 0 entities
2025-05-20 16:19:06 - ai_brain_model.conversation.conversation_knowledge - INFO - Added conversation pattern: When concluding a conversation, summarize key poin...
2025-05-20 16:19:06 - ai_brain_model.knowledge_base.vector_store - INFO - Saved 26 vectors to vector_store.pkl
2025-05-20 16:19:06 - ai_brain_model.knowledge_base.knowledge_base - INFO - Added knowledge with ID 25 and 0 entities
2025-05-20 16:19:06 - ai_brain_model.conversation.conversation_knowledge - INFO - Added conversation pattern: For farewells, express pleasure in the conversatio...
2025-05-20 16:19:06 - ai_brain_model.knowledge_base.vector_store - INFO - Saved 27 vectors to vector_store.pkl
2025-05-20 16:19:07 - ai_brain_model.knowledge_base.knowledge_base - INFO - Added knowledge with ID 26 and 0 entities
2025-05-20 16:19:07 - ai_brain_model.conversation.conversation_knowledge - INFO - Added conversation pattern: If the conversation has been lengthy, acknowledge ...
2025-05-20 16:19:07 - ai_brain_model.knowledge_base.vector_store - INFO - Saved 28 vectors to vector_store.pkl
2025-05-20 16:19:07 - ai_brain_model.knowledge_base.knowledge_base - INFO - Added knowledge with ID 27 and 0 entities
2025-05-20 16:19:07 - ai_brain_model.conversation.conversation_knowledge - INFO - Added conversation pattern: When asked a question, provide a direct answer fir...
2025-05-20 16:19:07 - ai_brain_model.knowledge_base.vector_store - INFO - Saved 29 vectors to vector_store.pkl
2025-05-20 16:19:07 - ai_brain_model.knowledge_base.knowledge_base - INFO - Added knowledge with ID 28 and 0 entities
2025-05-20 16:19:07 - ai_brain_model.conversation.conversation_knowledge - INFO - Added conversation pattern: If the conversation stalls, ask an open-ended ques...
2025-05-20 16:19:07 - ai_brain_model.knowledge_base.vector_store - INFO - Saved 30 vectors to vector_store.pkl
2025-05-20 16:19:07 - ai_brain_model.knowledge_base.knowledge_base - INFO - Added knowledge with ID 29 and 0 entities
2025-05-20 16:19:07 - ai_brain_model.conversation.conversation_knowledge - INFO - Added conversation pattern: When the user shares something personal, acknowled...
2025-05-20 16:19:08 - ai_brain_model.conversation.conversation_knowledge - INFO - Added 10 conversation patterns to the knowledge base
2025-05-20 16:19:08 - ai_brain_model.conversation.conversation_knowledge - INFO - Successfully added 29 conversation knowledge items
2025-05-20 16:19:08 - ai_brain_model.knowledge_base.vector_store - INFO - Saved 31 vectors to vector_store.pkl
2025-05-20 16:19:09 - ai_brain_model.knowledge_base.knowledge_base - INFO - Added knowledge with ID 30 and 0 entities
2025-05-20 16:19:09 - ai_brain_model.knowledge_base.vector_store - INFO - Saved 32 vectors to vector_store.pkl
2025-05-20 16:19:09 - ai_brain_model.knowledge_base.knowledge_base - INFO - Added knowledge with ID 31 and 0 entities
2025-05-20 16:19:09 - ai_brain_model.knowledge_base.vector_store - INFO - Saved 33 vectors to vector_store.pkl
2025-05-20 16:19:09 - ai_brain_model.knowledge_base.knowledge_base - INFO - Added knowledge with ID 32 and 0 entities
2025-05-20 16:19:09 - ai_brain_model.knowledge_base.vector_store - INFO - Saved 34 vectors to vector_store.pkl
2025-05-20 16:19:10 - ai_brain_model.knowledge_base.knowledge_base - INFO - Added knowledge with ID 33 and 0 entities
2025-05-20 16:19:10 - ai_brain_model.knowledge_base.vector_store - INFO - Saved 35 vectors to vector_store.pkl
2025-05-20 16:19:10 - ai_brain_model.knowledge_base.knowledge_base - INFO - Added knowledge with ID 34 and 0 entities
2025-05-20 16:19:10 - ai_brain_model.knowledge_base.vector_store - INFO - Saved 36 vectors to vector_store.pkl
2025-05-20 16:19:10 - ai_brain_model.knowledge_base.knowledge_base - INFO - Added knowledge with ID 35 and 0 entities
2025-05-20 16:19:10 - ai_brain_model.knowledge_base.vector_store - INFO - Saved 37 vectors to vector_store.pkl
2025-05-20 16:19:10 - ai_brain_model.knowledge_base.knowledge_base - INFO - Added knowledge with ID 36 and 0 entities
2025-05-20 16:19:10 - ai_brain_model.knowledge_base.vector_store - INFO - Saved 38 vectors to vector_store.pkl
2025-05-20 16:19:11 - ai_brain_model.knowledge_base.knowledge_base - INFO - Added knowledge with ID 37 and 0 entities
2025-05-20 16:19:11 - ai_brain_model.knowledge_base.vector_store - INFO - Saved 39 vectors to vector_store.pkl
2025-05-20 16:19:11 - ai_brain_model.knowledge_base.knowledge_base - INFO - Added knowledge with ID 38 and 0 entities
2025-05-20 16:19:11 - ai_brain_model.knowledge_base.vector_store - INFO - Saved 40 vectors to vector_store.pkl
2025-05-20 16:19:11 - ai_brain_model.knowledge_base.knowledge_base - INFO - Added knowledge with ID 39 and 0 entities
2025-05-20 16:19:11 - ai_brain_model.knowledge_base.vector_store - INFO - Saved 41 vectors to vector_store.pkl
2025-05-20 16:19:11 - ai_brain_model.knowledge_base.knowledge_base - INFO - Added knowledge with ID 40 and 0 entities
2025-05-20 16:19:11 - ai_brain_model.knowledge_base.vector_store - INFO - Saved 42 vectors to vector_store.pkl
2025-05-20 16:19:11 - ai_brain_model.knowledge_base.knowledge_base - INFO - Added knowledge with ID 41 and 0 entities
2025-05-20 16:19:11 - ai_brain_model.knowledge_base.vector_store - INFO - Saved 43 vectors to vector_store.pkl
2025-05-20 16:19:12 - ai_brain_model.knowledge_base.knowledge_base - INFO - Added knowledge with ID 42 and 0 entities
2025-05-20 16:19:12 - ai_brain_model.knowledge_base.vector_store - INFO - Saved 44 vectors to vector_store.pkl
2025-05-20 16:19:12 - ai_brain_model.knowledge_base.knowledge_base - INFO - Added knowledge with ID 43 and 0 entities
2025-05-20 16:19:12 - ai_brain_model.knowledge_base.vector_store - INFO - Saved 45 vectors to vector_store.pkl
2025-05-20 16:19:12 - ai_brain_model.knowledge_base.knowledge_base - INFO - Added knowledge with ID 44 and 0 entities
2025-05-20 16:19:12 - ai_brain_model.knowledge_base.vector_store - INFO - Saved 46 vectors to vector_store.pkl
2025-05-20 16:19:13 - ai_brain_model.knowledge_base.knowledge_base - INFO - Added knowledge with ID 45 and 0 entities
2025-05-20 16:19:13 - ai_brain_model.knowledge_base.vector_store - INFO - Saved 47 vectors to vector_store.pkl
2025-05-20 16:19:13 - ai_brain_model.knowledge_base.knowledge_base - INFO - Added knowledge with ID 46 and 0 entities
2025-05-20 16:19:13 - ai_brain_model.knowledge_base.vector_store - INFO - Saved 48 vectors to vector_store.pkl
2025-05-20 16:19:13 - ai_brain_model.knowledge_base.knowledge_base - INFO - Added knowledge with ID 47 and 0 entities
2025-05-20 16:19:14 - ai_brain_model.knowledge_base.vector_store - INFO - Saved 49 vectors to vector_store.pkl
2025-05-20 16:19:14 - ai_brain_model.knowledge_base.knowledge_base - INFO - Added knowledge with ID 48 and 0 entities
2025-05-20 16:19:14 - ai_brain_model.knowledge_base.vector_store - INFO - Saved 50 vectors to vector_store.pkl
2025-05-20 16:19:14 - ai_brain_model.knowledge_base.knowledge_base - INFO - Added knowledge with ID 49 and 0 entities
2025-05-20 16:19:14 - ai_brain_model.knowledge_base.vector_store - INFO - Saved 51 vectors to vector_store.pkl
2025-05-20 16:19:14 - ai_brain_model.knowledge_base.knowledge_base - INFO - Added knowledge with ID 50 and 0 entities
2025-05-20 16:19:14 - ai_brain_model.knowledge_base.vector_store - INFO - Saved 52 vectors to vector_store.pkl
2025-05-20 16:19:14 - ai_brain_model.knowledge_base.knowledge_base - INFO - Added knowledge with ID 51 and 0 entities
2025-05-20 16:19:14 - ai_brain_model.knowledge_base.vector_store - INFO - Saved 53 vectors to vector_store.pkl
2025-05-20 16:19:15 - ai_brain_model.knowledge_base.knowledge_base - INFO - Added knowledge with ID 52 and 0 entities
2025-05-20 16:19:15 - ai_brain_model.knowledge_base.vector_store - INFO - Saved 54 vectors to vector_store.pkl
2025-05-20 16:19:15 - ai_brain_model.knowledge_base.knowledge_base - INFO - Added knowledge with ID 53 and 0 entities
2025-05-20 16:19:15 - __main__ - INFO - Added 24 advanced conversation strategies to the knowledge base
2025-05-20 16:19:15 - ai_brain_model.knowledge_base.vector_store - INFO - Saved 55 vectors to vector_store.pkl
2025-05-20 16:19:15 - ai_brain_model.knowledge_base.knowledge_base - INFO - Added knowledge with ID 54 and 0 entities
2025-05-20 16:19:15 - ai_brain_model.knowledge_base.vector_store - INFO - Saved 56 vectors to vector_store.pkl
2025-05-20 16:19:15 - ai_brain_model.knowledge_base.knowledge_base - INFO - Added knowledge with ID 55 and 0 entities
2025-05-20 16:19:15 - ai_brain_model.knowledge_base.vector_store - INFO - Saved 57 vectors to vector_store.pkl
2025-05-20 16:19:15 - ai_brain_model.knowledge_base.knowledge_base - INFO - Added knowledge with ID 56 and 0 entities
2025-05-20 16:19:15 - ai_brain_model.knowledge_base.vector_store - INFO - Saved 58 vectors to vector_store.pkl
2025-05-20 16:19:16 - ai_brain_model.knowledge_base.knowledge_base - INFO - Added knowledge with ID 57 and 0 entities
2025-05-20 16:19:16 - ai_brain_model.knowledge_base.vector_store - INFO - Saved 59 vectors to vector_store.pkl
2025-05-20 16:19:16 - ai_brain_model.knowledge_base.knowledge_base - INFO - Added knowledge with ID 58 and 0 entities
2025-05-20 16:19:16 - ai_brain_model.knowledge_base.vector_store - INFO - Saved 60 vectors to vector_store.pkl
2025-05-20 16:19:16 - ai_brain_model.knowledge_base.knowledge_base - INFO - Added knowledge with ID 59 and 0 entities
2025-05-20 16:19:16 - ai_brain_model.knowledge_base.vector_store - INFO - Saved 61 vectors to vector_store.pkl
2025-05-20 16:19:16 - ai_brain_model.knowledge_base.knowledge_base - INFO - Added knowledge with ID 60 and 0 entities
2025-05-20 16:19:16 - ai_brain_model.knowledge_base.vector_store - INFO - Saved 62 vectors to vector_store.pkl
2025-05-20 16:19:17 - ai_brain_model.knowledge_base.knowledge_base - INFO - Added knowledge with ID 61 and 0 entities
2025-05-20 16:19:17 - ai_brain_model.knowledge_base.vector_store - INFO - Saved 63 vectors to vector_store.pkl
2025-05-20 16:19:17 - ai_brain_model.knowledge_base.knowledge_base - INFO - Added knowledge with ID 62 and 0 entities
2025-05-20 16:19:17 - ai_brain_model.knowledge_base.vector_store - INFO - Saved 64 vectors to vector_store.pkl
2025-05-20 16:19:17 - ai_brain_model.knowledge_base.knowledge_base - INFO - Added knowledge with ID 63 and 0 entities
2025-05-20 16:19:17 - ai_brain_model.knowledge_base.vector_store - INFO - Saved 65 vectors to vector_store.pkl
2025-05-20 16:19:17 - ai_brain_model.knowledge_base.knowledge_base - INFO - Added knowledge with ID 64 and 0 entities
2025-05-20 16:19:17 - ai_brain_model.knowledge_base.vector_store - INFO - Saved 66 vectors to vector_store.pkl
2025-05-20 16:19:17 - ai_brain_model.knowledge_base.knowledge_base - INFO - Added knowledge with ID 65 and 0 entities
2025-05-20 16:19:17 - ai_brain_model.knowledge_base.vector_store - INFO - Saved 67 vectors to vector_store.pkl
2025-05-20 16:19:18 - ai_brain_model.knowledge_base.knowledge_base - INFO - Added knowledge with ID 66 and 0 entities
2025-05-20 16:19:18 - ai_brain_model.knowledge_base.vector_store - INFO - Saved 68 vectors to vector_store.pkl
2025-05-20 16:19:18 - ai_brain_model.knowledge_base.knowledge_base - INFO - Added knowledge with ID 67 and 0 entities
2025-05-20 16:19:18 - ai_brain_model.knowledge_base.vector_store - INFO - Saved 69 vectors to vector_store.pkl
2025-05-20 16:19:18 - ai_brain_model.knowledge_base.knowledge_base - INFO - Added knowledge with ID 68 and 0 entities
2025-05-20 16:19:18 - ai_brain_model.knowledge_base.vector_store - INFO - Saved 70 vectors to vector_store.pkl
2025-05-20 16:19:18 - ai_brain_model.knowledge_base.knowledge_base - INFO - Added knowledge with ID 69 and 0 entities
2025-05-20 16:19:18 - ai_brain_model.knowledge_base.vector_store - INFO - Saved 71 vectors to vector_store.pkl
2025-05-20 16:19:19 - ai_brain_model.knowledge_base.knowledge_base - INFO - Added knowledge with ID 70 and 0 entities
2025-05-20 16:19:19 - ai_brain_model.knowledge_base.vector_store - INFO - Saved 72 vectors to vector_store.pkl
2025-05-20 16:19:19 - ai_brain_model.knowledge_base.knowledge_base - INFO - Added knowledge with ID 71 and 0 entities
2025-05-20 16:19:19 - __main__ - INFO - Added 18 emotional intelligence items to the knowledge base
2025-05-20 16:19:19 - ai_brain_model.knowledge_base.vector_store - ERROR - Error saving vectors: [Errno 13] Permission denied: 'vector_store.pkl'
2025-05-20 16:19:19 - ai_brain_model.knowledge_base.knowledge_base - INFO - Added knowledge with ID 72 and 0 entities
2025-05-20 16:19:19 - ai_brain_model.knowledge_base.vector_store - INFO - Saved 74 vectors to vector_store.pkl
2025-05-20 16:19:19 - ai_brain_model.knowledge_base.knowledge_base - INFO - Added knowledge with ID 73 and 0 entities
2025-05-20 16:19:19 - ai_brain_model.knowledge_base.vector_store - INFO - Saved 75 vectors to vector_store.pkl
2025-05-20 16:19:19 - ai_brain_model.knowledge_base.knowledge_base - INFO - Added knowledge with ID 74 and 0 entities
2025-05-20 16:19:19 - ai_brain_model.knowledge_base.vector_store - INFO - Saved 76 vectors to vector_store.pkl
2025-05-20 16:19:20 - ai_brain_model.knowledge_base.knowledge_base - INFO - Added knowledge with ID 75 and 0 entities
2025-05-20 16:19:20 - ai_brain_model.knowledge_base.vector_store - INFO - Saved 77 vectors to vector_store.pkl
2025-05-20 16:19:20 - ai_brain_model.knowledge_base.knowledge_base - INFO - Added knowledge with ID 76 and 0 entities
2025-05-20 16:19:20 - ai_brain_model.knowledge_base.vector_store - INFO - Saved 78 vectors to vector_store.pkl
2025-05-20 16:19:21 - ai_brain_model.knowledge_base.knowledge_base - INFO - Added knowledge with ID 77 and 0 entities
2025-05-20 16:19:21 - ai_brain_model.knowledge_base.vector_store - INFO - Saved 79 vectors to vector_store.pkl
2025-05-20 16:19:21 - ai_brain_model.knowledge_base.knowledge_base - INFO - Added knowledge with ID 78 and 0 entities
2025-05-20 16:19:21 - ai_brain_model.knowledge_base.vector_store - INFO - Saved 80 vectors to vector_store.pkl
2025-05-20 16:19:21 - ai_brain_model.knowledge_base.knowledge_base - INFO - Added knowledge with ID 79 and 0 entities
2025-05-20 16:19:21 - ai_brain_model.knowledge_base.vector_store - INFO - Saved 81 vectors to vector_store.pkl
2025-05-20 16:19:21 - ai_brain_model.knowledge_base.knowledge_base - INFO - Added knowledge with ID 80 and 0 entities
2025-05-20 16:19:21 - ai_brain_model.knowledge_base.vector_store - INFO - Saved 82 vectors to vector_store.pkl
2025-05-20 16:19:21 - ai_brain_model.knowledge_base.knowledge_base - INFO - Added knowledge with ID 81 and 0 entities
2025-05-20 16:19:21 - ai_brain_model.knowledge_base.vector_store - INFO - Saved 83 vectors to vector_store.pkl
2025-05-20 16:19:22 - ai_brain_model.knowledge_base.knowledge_base - INFO - Added knowledge with ID 82 and 0 entities
2025-05-20 16:19:22 - ai_brain_model.knowledge_base.vector_store - INFO - Saved 84 vectors to vector_store.pkl
2025-05-20 16:19:22 - ai_brain_model.knowledge_base.knowledge_base - INFO - Added knowledge with ID 83 and 0 entities
2025-05-20 16:19:22 - ai_brain_model.knowledge_base.vector_store - INFO - Saved 85 vectors to vector_store.pkl
2025-05-20 16:19:22 - ai_brain_model.knowledge_base.knowledge_base - INFO - Added knowledge with ID 84 and 0 entities
2025-05-20 16:19:22 - ai_brain_model.knowledge_base.vector_store - INFO - Saved 86 vectors to vector_store.pkl
2025-05-20 16:19:22 - ai_brain_model.knowledge_base.knowledge_base - INFO - Added knowledge with ID 85 and 0 entities
2025-05-20 16:19:22 - ai_brain_model.knowledge_base.vector_store - INFO - Saved 87 vectors to vector_store.pkl
2025-05-20 16:19:23 - ai_brain_model.knowledge_base.knowledge_base - INFO - Added knowledge with ID 86 and 0 entities
2025-05-20 16:19:23 - ai_brain_model.knowledge_base.vector_store - INFO - Saved 88 vectors to vector_store.pkl
2025-05-20 16:19:23 - ai_brain_model.knowledge_base.knowledge_base - INFO - Added knowledge with ID 87 and 0 entities
2025-05-20 16:19:23 - ai_brain_model.knowledge_base.vector_store - INFO - Saved 89 vectors to vector_store.pkl
2025-05-20 16:19:23 - ai_brain_model.knowledge_base.knowledge_base - INFO - Added knowledge with ID 88 and 0 entities
2025-05-20 16:19:23 - ai_brain_model.knowledge_base.vector_store - INFO - Saved 90 vectors to vector_store.pkl
2025-05-20 16:19:23 - ai_brain_model.knowledge_base.knowledge_base - INFO - Added knowledge with ID 89 and 0 entities
2025-05-20 16:19:23 - ai_brain_model.knowledge_base.vector_store - INFO - Saved 91 vectors to vector_store.pkl
2025-05-20 16:19:23 - ai_brain_model.knowledge_base.knowledge_base - INFO - Added knowledge with ID 90 and 0 entities
2025-05-20 16:19:23 - ai_brain_model.knowledge_base.vector_store - INFO - Saved 92 vectors to vector_store.pkl
2025-05-20 16:19:24 - ai_brain_model.knowledge_base.knowledge_base - INFO - Added knowledge with ID 91 and 0 entities
2025-05-20 16:19:24 - __main__ - INFO - Added 20 real-world conversation patterns to the knowledge base
2025-05-20 16:19:24 - ai_brain_model.knowledge_base.vector_store - INFO - Saved 93 vectors to vector_store.pkl
2025-05-20 16:19:24 - ai_brain_model.knowledge_base.knowledge_base - INFO - Added knowledge with ID 92 and 0 entities
2025-05-20 16:19:24 - ai_brain_model.knowledge_base.vector_store - INFO - Saved 94 vectors to vector_store.pkl
2025-05-20 16:19:24 - ai_brain_model.knowledge_base.knowledge_base - INFO - Added knowledge with ID 93 and 0 entities
2025-05-20 16:19:24 - ai_brain_model.knowledge_base.vector_store - INFO - Saved 95 vectors to vector_store.pkl
2025-05-20 16:19:24 - ai_brain_model.knowledge_base.knowledge_base - INFO - Added knowledge with ID 94 and 0 entities
2025-05-20 16:19:24 - ai_brain_model.knowledge_base.vector_store - INFO - Saved 96 vectors to vector_store.pkl
2025-05-20 16:19:25 - ai_brain_model.knowledge_base.knowledge_base - INFO - Added knowledge with ID 95 and 0 entities
2025-05-20 16:19:25 - ai_brain_model.knowledge_base.vector_store - INFO - Saved 97 vectors to vector_store.pkl
2025-05-20 16:19:25 - ai_brain_model.knowledge_base.knowledge_base - INFO - Added knowledge with ID 96 and 0 entities
2025-05-20 16:19:25 - ai_brain_model.knowledge_base.vector_store - INFO - Saved 98 vectors to vector_store.pkl
2025-05-20 16:19:25 - ai_brain_model.knowledge_base.knowledge_base - INFO - Added knowledge with ID 97 and 0 entities
2025-05-20 16:19:25 - ai_brain_model.knowledge_base.vector_store - INFO - Saved 99 vectors to vector_store.pkl
2025-05-20 16:19:25 - ai_brain_model.knowledge_base.knowledge_base - INFO - Added knowledge with ID 98 and 0 entities
2025-05-20 16:19:25 - ai_brain_model.knowledge_base.vector_store - INFO - Saved 100 vectors to vector_store.pkl
2025-05-20 16:19:26 - ai_brain_model.knowledge_base.knowledge_base - INFO - Added knowledge with ID 99 and 0 entities
2025-05-20 16:19:26 - ai_brain_model.knowledge_base.vector_store - INFO - Saved 101 vectors to vector_store.pkl
2025-05-20 16:19:26 - ai_brain_model.knowledge_base.knowledge_base - INFO - Added knowledge with ID 100 and 0 entities
2025-05-20 16:19:26 - ai_brain_model.knowledge_base.vector_store - INFO - Saved 102 vectors to vector_store.pkl
2025-05-20 16:19:26 - ai_brain_model.knowledge_base.knowledge_base - INFO - Added knowledge with ID 101 and 0 entities
2025-05-20 16:19:26 - ai_brain_model.knowledge_base.vector_store - INFO - Saved 103 vectors to vector_store.pkl
2025-05-20 16:19:26 - ai_brain_model.knowledge_base.knowledge_base - INFO - Added knowledge with ID 102 and 0 entities
2025-05-20 16:19:26 - ai_brain_model.knowledge_base.vector_store - INFO - Saved 104 vectors to vector_store.pkl
2025-05-20 16:19:27 - ai_brain_model.knowledge_base.knowledge_base - INFO - Added knowledge with ID 103 and 0 entities
2025-05-20 16:19:27 - ai_brain_model.knowledge_base.vector_store - INFO - Saved 105 vectors to vector_store.pkl
2025-05-20 16:19:27 - ai_brain_model.knowledge_base.knowledge_base - INFO - Added knowledge with ID 104 and 0 entities
2025-05-20 16:19:27 - ai_brain_model.knowledge_base.vector_store - INFO - Saved 106 vectors to vector_store.pkl
2025-05-20 16:19:27 - ai_brain_model.knowledge_base.knowledge_base - INFO - Added knowledge with ID 105 and 0 entities
2025-05-20 16:19:27 - ai_brain_model.knowledge_base.vector_store - INFO - Saved 107 vectors to vector_store.pkl
2025-05-20 16:19:27 - ai_brain_model.knowledge_base.knowledge_base - INFO - Added knowledge with ID 106 and 0 entities
2025-05-20 16:19:27 - __main__ - INFO - Added 15 personality traits to the knowledge base
2025-05-20 16:19:27 - __main__ - INFO - Knowledge base population complete. Added 77 items:
2025-05-20 16:19:27 - __main__ - INFO - - Advanced conversation strategies: 24
2025-05-20 16:19:27 - __main__ - INFO - - Emotional intelligence data: 18
2025-05-20 16:19:27 - __main__ - INFO - - Real-world conversation patterns: 20
2025-05-20 16:19:27 - __main__ - INFO - - Personality traits: 15
2025-05-20 16:19:27 - __main__ - INFO - Knowledge base saved to nova_knowledge_base.db
2025-05-20 16:45:16 - root - INFO - Logging configured with level=info, log_to_console=True, log_to_file=True
2025-05-20 16:49:47 - root - INFO - Logging configured with level=info, log_to_console=True, log_to_file=True
2025-05-20 16:54:54 - root - INFO - Logging configured with level=info, log_to_console=True, log_to_file=True
2025-05-20 17:06:36 - root - INFO - Logging configured with level=info, log_to_console=True, log_to_file=True
2025-05-20 17:08:14 - root - INFO - Logging configured with level=info, log_to_console=True, log_to_file=True
2025-05-20 17:12:10 - root - INFO - Logging configured with level=info, log_to_console=True, log_to_file=True
2025-05-20 17:20:54 - root - INFO - Logging configured with level=info, log_to_console=True, log_to_file=True
2025-05-29 17:31:07 - root - INFO - Logging configured with level=info, log_to_console=True, log_to_file=True
2025-05-30 19:49:38 - root - INFO - Logging configured with level=info, log_to_console=True, log_to_file=True
2025-05-30 20:08:53 - root - INFO - Logging configured with level=info, log_to_console=True, log_to_file=True
2025-05-30 20:16:51 - root - INFO - Logging configured with level=info, log_to_console=True, log_to_file=True
2025-05-30 20:17:43 - root - INFO - Logging configured with level=info, log_to_console=True, log_to_file=True
2025-05-30 20:21:08 - root - INFO - Logging configured with level=info, log_to_console=True, log_to_file=True
2025-05-30 20:24:39 - root - INFO - Logging configured with level=info, log_to_console=True, log_to_file=True
2025-05-30 20:36:20 - root - INFO - Logging configured with level=info, log_to_console=True, log_to_file=True
2025-05-30 20:46:53 - root - INFO - Logging configured with level=info, log_to_console=True, log_to_file=True
2025-05-30 20:56:53 - root - INFO - Logging configured with level=info, log_to_console=True, log_to_file=True
2025-05-30 21:32:47 - root - INFO - Logging configured with level=info, log_to_console=True, log_to_file=True
2025-05-30 22:06:13 - root - INFO - Logging configured with level=info, log_to_console=True, log_to_file=True
