"""
Test script to identify and debug Nova errors.

This script will help identify the specific errors you're encountering
when sending messages to Nova.
"""

import os
import sys
import traceback
import logging

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up basic logging first
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("test_nova_errors.log"),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

def test_imports():
    """Test importing Nova components."""
    print("🔍 Testing imports...")
    
    try:
        logger.info("Testing basic imports...")
        
        # Test basic AI brain model imports
        from ai_brain_model.utils.logging_config import setup_logging, get_logger
        print("✅ Logging config imported successfully")
        
        from ai_brain_model.utils.error_tracker import setup_enhanced_error_handling
        print("✅ Error tracker imported successfully")
        
        # Test Nova model integration
        from nova_model_integration import NovaModelWrapper
        print("✅ NovaModelWrapper imported successfully")
        
        from nova_integration import NovaAI
        print("✅ NovaAI imported successfully")
        
        return True
        
    except Exception as e:
        print(f"❌ Import error: {e}")
        logger.error(f"Import error: {e}", exc_info=True)
        return False

def test_file_existence():
    """Test if required files exist."""
    print("\n🔍 Testing file existence...")
    
    files_to_check = [
        ("Model", "output_expanded/nova_model_expanded_epoch_17.pt"),
        ("Tokenizer", "output_bpe/nova_bpe_tokenizer.json"),
        ("Knowledge Base", "nova_knowledge_base.db")
    ]
    
    all_exist = True
    for file_type, file_path in files_to_check:
        if os.path.exists(file_path):
            size = os.path.getsize(file_path)
            print(f"✅ {file_type}: {file_path} ({size:,} bytes)")
        else:
            print(f"❌ {file_type}: {file_path} (NOT FOUND)")
            all_exist = False
    
    return all_exist

def test_model_loading():
    """Test loading the Nova model."""
    print("\n🔍 Testing model loading...")
    
    try:
        from nova_model_integration import NovaModelWrapper
        
        print("Creating NovaModelWrapper...")
        model_wrapper = NovaModelWrapper(
            model_path="output_expanded/nova_model_expanded_epoch_17.pt",
            tokenizer_path="output_bpe/nova_bpe_tokenizer.json",
            use_memory_optimization=True
        )
        print("✅ Model wrapper created successfully")
        
        # Test a simple generation
        print("Testing text generation...")
        test_prompt = "Hello, how are you?"
        response = model_wrapper.generate_text(test_prompt)
        print(f"✅ Generated response: '{response}'")
        
        return True
        
    except Exception as e:
        print(f"❌ Model loading error: {e}")
        logger.error(f"Model loading error: {e}", exc_info=True)
        return False

def test_nova_integration():
    """Test Nova AI integration."""
    print("\n🔍 Testing Nova AI integration...")
    
    try:
        from nova_model_integration import NovaModelWrapper
        from nova_integration import NovaAI
        
        print("Creating model wrapper...")
        model_wrapper = NovaModelWrapper(
            model_path="output_expanded/nova_model_expanded_epoch_17.pt",
            tokenizer_path="output_bpe/nova_bpe_tokenizer.json",
            use_memory_optimization=True
        )
        
        print("Creating Nova AI integration...")
        nova = NovaAI(
            model_function=model_wrapper.generate_text,
            knowledge_base_path="nova_knowledge_base.db",
            enable_tools=True
        )
        
        print("Starting session...")
        session_id = nova.start_session()
        print(f"✅ Session started: {session_id}")
        
        # Test message processing
        print("Testing message processing...")
        test_message = "Hello Nova, how are you today?"
        result = nova.process_message(test_message)
        
        print(f"✅ Message processed successfully")
        print(f"Response: '{result.get('response', 'No response')}'")
        
        return True
        
    except Exception as e:
        print(f"❌ Nova integration error: {e}")
        logger.error(f"Nova integration error: {e}", exc_info=True)
        return False

def test_enhanced_cli():
    """Test the enhanced CLI interface."""
    print("\n🔍 Testing enhanced CLI interface...")
    
    try:
        from enhanced_nova_cli import EnhancedNovaInterface
        
        print("Creating enhanced interface...")
        interface = EnhancedNovaInterface()
        
        print("Initializing Nova...")
        interface.initialize_nova(
            model_path="output_expanded/nova_model_expanded_epoch_17.pt",
            tokenizer_path="output_bpe/nova_bpe_tokenizer.json",
            kb_path="nova_knowledge_base.db"
        )
        
        print("Testing message processing...")
        response = interface.process_message("Hello, this is a test message.")
        print(f"✅ Enhanced CLI test successful")
        print(f"Response: '{response}'")
        
        return True
        
    except Exception as e:
        print(f"❌ Enhanced CLI error: {e}")
        logger.error(f"Enhanced CLI error: {e}", exc_info=True)
        return False

def main():
    """Run all tests to identify errors."""
    print("🚀 Nova Error Diagnosis Tool")
    print("=" * 50)
    
    tests = [
        ("File Existence", test_file_existence),
        ("Imports", test_imports),
        ("Model Loading", test_model_loading),
        ("Nova Integration", test_nova_integration),
        ("Enhanced CLI", test_enhanced_cli)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} failed with unexpected error: {e}")
            logger.error(f"{test_name} failed: {e}", exc_info=True)
            results[test_name] = False
    
    # Summary
    print(f"\n{'='*50}")
    print("📊 TEST SUMMARY")
    print("=" * 50)
    
    passed = 0
    total = len(tests)
    
    for test_name, result in results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Nova should be working correctly.")
    else:
        print("🚨 Some tests failed. Check the errors above and the log file.")
        print("Log file: test_nova_errors.log")
    
    print("\n💡 If you're still seeing errors when using Nova:")
    print("1. Run this script to see which component is failing")
    print("2. Check the detailed error logs")
    print("3. Try the enhanced CLI: python enhanced_nova_cli.py")

if __name__ == "__main__":
    main()
