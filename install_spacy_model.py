"""
Install spaCy model for Nova AI.

This script installs the en_core_web_md spaCy model that Nova AI needs
for enhanced entity extraction.
"""

import subprocess
import sys
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def install_spacy_model():
    """Install the spaCy en_core_web_md model."""
    try:
        logger.info("Installing spaCy en_core_web_md model...")
        
        # Install the model
        result = subprocess.run([
            sys.executable, "-m", "spacy", "download", "en_core_web_md"
        ], capture_output=True, text=True, check=True)
        
        logger.info("spaCy model installed successfully!")
        logger.info(f"Output: {result.stdout}")
        
        # Test the installation
        logger.info("Testing spaCy model installation...")
        import spacy
        nlp = spacy.load("en_core_web_md")
        doc = nlp("Hello, this is a test.")
        logger.info(f"Test successful! Processed: '{doc.text}'")
        
        return True
        
    except subprocess.CalledProcessError as e:
        logger.error(f"Failed to install spaCy model: {e}")
        logger.error(f"Error output: {e.stderr}")
        return False
    except Exception as e:
        logger.error(f"Error testing spaCy model: {e}")
        return False

def main():
    """Main function."""
    logger.info("Starting spaCy model installation for Nova AI...")
    
    # Check if spaCy is installed
    try:
        import spacy
        logger.info("spaCy is installed.")
    except ImportError:
        logger.error("spaCy is not installed. Please install it first with: pip install spacy")
        return False
    
    # Try to load the model first
    try:
        nlp = spacy.load("en_core_web_md")
        logger.info("spaCy en_core_web_md model is already installed!")
        return True
    except OSError:
        logger.info("spaCy en_core_web_md model not found. Installing...")
        return install_spacy_model()

if __name__ == "__main__":
    success = main()
    if success:
        print("\n✅ spaCy model installation completed successfully!")
        print("Nova AI can now use enhanced entity extraction.")
    else:
        print("\n❌ spaCy model installation failed.")
        print("Nova AI will continue to work with rule-based entity extraction.")
    
    input("\nPress Enter to continue...")
