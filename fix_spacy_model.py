"""
Fix spaCy Model Installation

This script properly installs the en_core_web_md model for Nova AI.
"""

import subprocess
import sys
import os
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_spacy_installation():
    """Check current spaCy installation status."""
    try:
        import spacy
        logger.info("✅ spaCy is installed")
        
        # Check version
        logger.info(f"spaCy version: {spacy.__version__}")
        
        # Try to load the model
        try:
            nlp = spacy.load("en_core_web_md")
            logger.info("✅ en_core_web_md model is already working!")
            return True
        except OSError as e:
            logger.info(f"❌ en_core_web_md model not found: {e}")
            return False
            
    except ImportError:
        logger.error("❌ spaCy is not installed")
        return False

def install_spacy_model_direct():
    """Install spaCy model using direct pip installation."""
    try:
        logger.info("Trying direct pip installation...")
        
        # Use pip to install the model directly
        cmd = [sys.executable, "-m", "pip", "install", 
               "https://github.com/explosion/spacy-models/releases/download/en_core_web_md-3.7.1/en_core_web_md-3.7.1-py3-none-any.whl"]
        
        logger.info(f"Running: {' '.join(cmd)}")
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            logger.info("✅ Model installed successfully via pip!")
            return True
        else:
            logger.error(f"❌ Pip installation failed: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        logger.error("❌ Installation timed out")
        return False
    except Exception as e:
        logger.error(f"❌ Direct installation failed: {e}")
        return False

def install_spacy_model_command():
    """Install spaCy model using spacy download command."""
    try:
        logger.info("Trying spacy download command...")
        
        # Try the standard spacy download
        cmd = [sys.executable, "-m", "spacy", "download", "en_core_web_md"]
        
        logger.info(f"Running: {' '.join(cmd)}")
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            logger.info("✅ Model installed successfully via spacy download!")
            return True
        else:
            logger.error(f"❌ Spacy download failed: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        logger.error("❌ Installation timed out")
        return False
    except Exception as e:
        logger.error(f"❌ Spacy download failed: {e}")
        return False

def test_model():
    """Test if the model works after installation."""
    try:
        import spacy
        nlp = spacy.load("en_core_web_md")
        
        # Test with a sample text
        doc = nlp("Hello, my name is John and I live in New York.")
        entities = [(ent.text, ent.label_) for ent in doc.ents]
        
        logger.info(f"✅ Model test successful! Found entities: {entities}")
        return True
        
    except Exception as e:
        logger.error(f"❌ Model test failed: {e}")
        return False

def main():
    """Main function to fix spaCy model installation."""
    logger.info("🔧 Fixing spaCy Model Installation...")
    logger.info("=" * 50)
    
    # Check current status
    if check_spacy_installation():
        logger.info("🎉 spaCy model is already working correctly!")
        return True
    
    # Try installation methods
    logger.info("\n📦 Attempting to install en_core_web_md model...")
    
    # Method 1: Direct pip installation
    logger.info("\n1. Trying direct pip installation...")
    if install_spacy_model_direct():
        if test_model():
            logger.info("🎉 Installation successful via pip!")
            return True
    
    # Method 2: spacy download command
    logger.info("\n2. Trying spacy download command...")
    if install_spacy_model_command():
        if test_model():
            logger.info("🎉 Installation successful via spacy download!")
            return True
    
    # If both methods fail
    logger.error("\n❌ Both installation methods failed.")
    logger.info("\n📝 Manual installation instructions:")
    logger.info("1. Open Command Prompt as Administrator")
    logger.info("2. Run: pip install https://github.com/explosion/spacy-models/releases/download/en_core_web_md-3.7.1/en_core_web_md-3.7.1-py3-none-any.whl")
    logger.info("3. Or try: python -m spacy download en_core_web_md")
    
    return False

if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n✅ spaCy model installation completed successfully!")
        print("Nova AI will now have enhanced entity extraction capabilities.")
    else:
        print("\n⚠️ spaCy model installation failed, but Nova AI will still work.")
        print("Nova will use rule-based entity extraction instead.")
    
    print("\nNote: This is not critical - Nova AI works fine without the enhanced model.")
    input("\nPress Enter to continue...")
