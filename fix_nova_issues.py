"""
Fix Nova AI Issues

This script fixes the main issues preventing Nova AI from working properly:
1. Parameter mismatch in model generation
2. Missing spaCy model
3. Improved error handling
"""

import os
import sys
import subprocess
import logging
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_and_install_spacy_model():
    """Check if spaCy model is installed and install if needed."""
    try:
        import spacy
        try:
            nlp = spacy.load("en_core_web_md")
            logger.info("✅ spaCy en_core_web_md model is already installed")
            return True
        except OSError:
            logger.info("Installing spaCy en_core_web_md model...")
            result = subprocess.run([
                sys.executable, "-m", "spacy", "download", "en_core_web_md"
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                logger.info("✅ spaCy model installed successfully")
                return True
            else:
                logger.warning(f"⚠️ Failed to install spaCy model: {result.stderr}")
                return False
    except ImportError:
        logger.warning("⚠️ spaCy not installed. Installing...")
        try:
            subprocess.run([sys.executable, "-m", "pip", "install", "spacy"], check=True)
            return check_and_install_spacy_model()  # Retry after installation
        except subprocess.CalledProcessError:
            logger.error("❌ Failed to install spaCy")
            return False

def check_model_files():
    """Check if required model files exist."""
    model_path = Path("output_expanded/nova_model_expanded_epoch_17.pt")
    tokenizer_path = Path("output_bpe/nova_bpe_tokenizer.json")
    
    issues = []
    
    if not model_path.exists():
        issues.append(f"Model file not found: {model_path}")
    else:
        logger.info(f"✅ Model file found: {model_path}")
    
    if not tokenizer_path.exists():
        issues.append(f"Tokenizer file not found: {tokenizer_path}")
    else:
        logger.info(f"✅ Tokenizer file found: {tokenizer_path}")
    
    return issues

def check_knowledge_base():
    """Check if knowledge base exists."""
    kb_path = Path("nova_knowledge_base.db")
    if not kb_path.exists():
        logger.info("Knowledge base not found. It will be created automatically.")
        return False
    else:
        logger.info(f"✅ Knowledge base found: {kb_path}")
        return True

def create_test_script():
    """Create a simple test script to verify the fixes."""
    test_script = """
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from nova_model_integration import NovaModelWrapper
from nova_integration import NovaAI

def test_nova():
    print("Testing Nova AI fixes...")
    
    try:
        # Create model wrapper
        model_wrapper = NovaModelWrapper(
            model_path="output_expanded/nova_model_expanded_epoch_17.pt",
            tokenizer_path="output_bpe/nova_bpe_tokenizer.json",
            use_memory_optimization=True
        )
        print("✅ Model wrapper created successfully")
        
        # Create Nova AI
        nova = NovaAI(
            model_function=model_wrapper.generate_text,
            knowledge_base_path="nova_knowledge_base.db",
            enable_tools=True
        )
        print("✅ Nova AI created successfully")
        
        # Start session
        session_id = nova.start_session()
        print(f"✅ Session started: {session_id}")
        
        # Test a simple greeting
        result = nova.process_message("Hello, how are you?")
        print(f"✅ Response generated: {result['response']}")
        
        # End session
        nova.end_session()
        print("✅ Session ended successfully")
        
        print("\\n🎉 All tests passed! Nova AI is working correctly.")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_nova()
"""
    
    with open("test_nova_fixes.py", "w") as f:
        f.write(test_script)
    
    logger.info("✅ Test script created: test_nova_fixes.py")

def main():
    """Main function to fix Nova AI issues."""
    logger.info("🔧 Starting Nova AI fixes...")
    
    # Check model files
    logger.info("Checking model files...")
    model_issues = check_model_files()
    if model_issues:
        logger.error("❌ Model file issues found:")
        for issue in model_issues:
            logger.error(f"  - {issue}")
        logger.error("Please ensure the model files are in the correct locations.")
        return False
    
    # Check knowledge base
    logger.info("Checking knowledge base...")
    check_knowledge_base()
    
    # Install spaCy model
    logger.info("Checking spaCy model...")
    check_and_install_spacy_model()
    
    # Create test script
    logger.info("Creating test script...")
    create_test_script()
    
    logger.info("🎉 Nova AI fixes completed!")
    logger.info("The main issue (parameter mismatch) has been fixed in nova_model_integration.py")
    logger.info("Run 'python test_nova_fixes.py' to test the fixes")
    
    return True

if __name__ == "__main__":
    success = main()
    if success:
        print("\n✅ Nova AI fixes completed successfully!")
        print("You can now run Nova AI using run_nova_complete.bat")
    else:
        print("\n❌ Some issues remain. Please check the logs above.")
    
    input("\nPress Enter to continue...")
