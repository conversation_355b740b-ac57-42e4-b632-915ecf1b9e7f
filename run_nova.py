"""
Run Nova AI

This script runs the Nova AI with the expanded epoch 17 model and populated knowledge base.
"""

import os
import sys
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("nova_run.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def main():
    """
    Main function to run Nova AI.
    """
    logger.info("🚀 Starting Nova AI...")
    logger.info("=" * 50)

    # Check if the knowledge base exists
    kb_path = "nova_knowledge_base.db"
    logger.info(f"📊 Checking for knowledge base at {kb_path}")

    if not os.path.exists(kb_path):
        logger.info("📊 Knowledge base not found. Populating knowledge base...")

        # Run the populate_knowledge_base.py script
        logger.info("🔄 Running populate_knowledge_base.py...")
        result = os.system(f"{sys.executable} populate_knowledge_base.py")
        logger.info(f"📊 populate_knowledge_base.py returned: {result}")

        if not os.path.exists(kb_path):
            logger.error(f"❌ Failed to create knowledge base at {kb_path}")
            print("Error: Failed to create knowledge base. See nova_run.log for details.")
            return
        else:
            logger.info("✅ Knowledge base created successfully")
    else:
        logger.info("✅ Knowledge base found")

    # Check if the model files exist with the correct names
    model_path = "output_expanded/nova_model_expanded_epoch_17.pt"
    tokenizer_path = "output_bpe/nova_bpe_tokenizer.json"

    logger.info(f"🤖 Checking for model at {model_path}")
    logger.info(f"📝 Checking for tokenizer at {tokenizer_path}")

    if not os.path.exists(model_path):
        logger.error(f"❌ Model file not found at {model_path}")
        print(f"Error: Model file not found at {model_path}")
        return
    else:
        logger.info("✅ Model file found")

    if not os.path.exists(tokenizer_path):
        logger.error(f"❌ Tokenizer file not found at {tokenizer_path}")
        print(f"Error: Tokenizer file not found at {tokenizer_path}")
        return
    else:
        logger.info("✅ Tokenizer file found")

    # Run the nova_model_integration.py script
    logger.info("🚀 Starting Nova AI integration...")
    logger.info("=" * 50)
    result = os.system(f"{sys.executable} nova_model_integration.py")
    logger.info(f"🏁 Nova AI integration finished with result: {result}")

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        logger.error(f"Error running Nova AI: {str(e)}", exc_info=True)
        print(f"Error: {str(e)}. See nova_run.log for details.")
